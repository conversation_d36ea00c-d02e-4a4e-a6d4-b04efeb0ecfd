# Site Settings & Logo Management Guide

## Overview

The Maddox Tees B2B platform now includes a comprehensive Site Settings system that allows administrators to manage site-wide configuration including logos, branding, contact information, and SEO settings through the Payload CMS admin panel.

## Features

### 🎨 **Branding & Logos**
- **Custom Logo Upload**: Upload your own logo to replace the default Payload logo
- **Logo Dimensions**: Configure custom width and height for your logo
- **Alt Text**: Set accessibility-friendly alt text for your logo
- **Favicon**: Upload a custom favicon for your site
- **AWS S3 Integration**: All logos are automatically stored in your S3 bucket under the "branding/" folder

### 📞 **Contact Information**
- Primary email address
- Phone number
- Business address

### 🌐 **Social Media Links**
- Facebook page URL
- Instagram profile URL
- Twitter/X profile URL
- LinkedIn company page URL

### 🔍 **SEO Settings**
- Default meta title for pages
- Default meta description for pages
- Default Open Graph image (1200x630px recommended)

## How to Access Site Settings

1. **Login to Admin Panel**: Navigate to `/admin` and login with admin credentials
2. **Find Site Settings**: In the admin navigation, look for "Site Configuration" group
3. **Click "Site Settings"**: This will open the global settings configuration

## Logo Management

### Uploading a New Logo

1. **Navigate to Site Settings** in the admin panel
2. **Go to "Branding & Logos" section**
3. **Click "Choose File" next to "Logo" field**
4. **Select your logo file** (SVG, PNG, or JPG recommended)
5. **Configure logo dimensions** if needed (default: 193x34px)
6. **Set alt text** for accessibility
7. **Save the settings**

### Logo Requirements

- **File Types**: SVG (recommended), PNG, JPG, WebP
- **Recommended Size**: Maximum 300px width for optimal display
- **Aspect Ratio**: Consider the default 193x34px ratio for best results
- **File Size**: Keep under 2MB for optimal loading performance

### Where Your Logo Appears

Once uploaded, your custom logo will automatically appear in:

- ✅ **Website Header** (main navigation)
- ✅ **Website Footer**
- ✅ **Admin Panel** (AdminBar title uses site name)
- ✅ **Login Page** (if you customize the login component)

## S3 Storage Integration

### Automatic S3 Upload

Your logos are automatically uploaded to AWS S3 with the following configuration:

- **Bucket**: `maddox-tees-media`
- **Region**: `ap-south-1`
- **Authentication**: Uses "harshluhar" AWS profile
- **Folder Structure**: All media files including logos are stored in the uploads folder:
  ```
  maddox-tees-media/
  ├── uploads/          (all media files including logos, images, documents)
  ├── dtf-designs/      (DTF sticker designs - future implementation)
  ├── custom-designs/   (custom t-shirt designs - future implementation)
  └── mockups/          (generated mockups - future implementation)
  ```

### Image Optimization

The system automatically generates multiple image sizes for optimal performance:

- **Thumbnail**: 300px width
- **Small**: 600px width
- **Medium**: 900px width
- **Large**: 1400px width
- **XLarge**: 1920px width
- **Square**: 500x500px
- **OG**: 1200x630px (for social sharing)

## Technical Implementation

### Components Updated

The following components now use dynamic logos from Site Settings:

1. **Logo Component** (`src/components/Logo/Logo.tsx`)
   - Client-side component with loading states
   - Fallback to default Payload logo if no custom logo

2. **LogoServer Component** (`src/components/Logo/LogoServer.tsx`)
   - Server-side component for SSR
   - Used in Header and Footer components

3. **Header Component** (`src/Header/Component.client.tsx`)
   - Uses dynamic logo with proper loading states

4. **Footer Component** (`src/Footer/Component.tsx`)
   - Server-side rendered with dynamic logo

5. **AdminBar Component** (`src/components/AdminBar/index.tsx`)
   - Uses site name from settings instead of "Dashboard"

### API Endpoints

- **GET `/api/site-settings`**: Fetch current site settings
  - Includes caching headers for performance
  - Returns default values on error
  - Includes related media files (depth: 2)

### Hooks & Utilities

- **`useSiteSettings()`**: Client-side hook for accessing site settings
- **`getSiteSettings()`**: Server-side function for SSR

## Fallback Behavior

The system is designed with robust fallback behavior:

### If No Custom Logo is Uploaded
- ✅ Falls back to default Payload logo
- ✅ Uses default dimensions (193x34px)
- ✅ Uses default alt text ("Maddox Tees Logo")

### If Site Settings API Fails
- ✅ Returns default site configuration
- ✅ Logs errors for debugging
- ✅ Continues to function normally

### If S3 Upload Fails
- ✅ Error is logged in admin panel
- ✅ User receives clear error message
- ✅ Can retry upload

## Best Practices

### Logo Design
1. **Use SVG format** when possible for crisp scaling
2. **Keep file size small** (under 500KB recommended)
3. **Design for dark/light themes** if your site supports theme switching
4. **Test on different screen sizes** to ensure readability

### SEO Optimization
1. **Set meaningful alt text** for accessibility
2. **Use descriptive meta titles** (50-60 characters)
3. **Write compelling meta descriptions** (150-160 characters)
4. **Upload high-quality OG images** for social sharing

### Performance
1. **Optimize images** before uploading
2. **Use appropriate dimensions** to avoid unnecessary scaling
3. **Monitor S3 storage usage** for cost management

## Troubleshooting

### Logo Not Appearing
1. **Check file upload**: Ensure the logo was uploaded successfully
2. **Verify file type**: Only image files are supported
3. **Clear browser cache**: Hard refresh the page (Ctrl+F5)
4. **Check console errors**: Look for JavaScript errors in browser console

### S3 Upload Issues
1. **Verify AWS credentials**: Ensure "harshluhar" profile is configured
2. **Check S3 permissions**: Verify bucket permissions allow uploads
3. **Check file size**: Ensure file is under the upload limit
4. **Review server logs**: Check terminal output for detailed errors

### API Errors
1. **Check network connectivity**: Ensure server is running
2. **Verify database connection**: Ensure MongoDB is accessible
3. **Check Payload configuration**: Verify globals are properly configured

## Support

For technical support or questions about the Site Settings system:

1. **Check the logs**: Review browser console and server terminal
2. **Verify configuration**: Ensure all environment variables are set
3. **Test with default values**: Try without custom uploads first
4. **Contact development team**: Provide specific error messages and steps to reproduce

---

**Note**: This feature requires admin-level access to the Payload CMS admin panel. Customer users will not see or have access to Site Settings configuration.
