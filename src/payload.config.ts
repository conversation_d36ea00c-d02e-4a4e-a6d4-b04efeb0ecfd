// storage-adapter-import-placeholder
import { mongooseAdapter } from '@payloadcms/db-mongodb'

import sharp from 'sharp' // sharp-import
import path from 'path'
import { buildConfig, PayloadRequest } from 'payload'
import { fileURLToPath } from 'url'

import { Categories } from './collections/Categories'
import { Customers } from './collections/Customers'
import { CustomTshirts } from './collections/CustomTshirts'

import { MarketingExpenses } from './collections/MarketingExpenses'
import { Media } from './collections/Media'
import { Orders } from './collections/Orders'
import { Pages } from './collections/Pages'
import { Partners } from './collections/Partners'
import { Posts } from './collections/Posts'
import { ProductCategories } from './collections/ProductCategories'
import { Products } from './collections/Products'
import { RecentWork } from './collections/RecentWork'
import { SolidTshirts } from './collections/SolidTshirts'
import { Testimonials } from './collections/Testimonials'
import { Users } from './collections/Users'
import { Footer } from './Footer/config'
import { Header } from './Header/config'
import { DTFPricing } from './globals/DTFPricing'
import { SiteSettings } from './globals/SiteSettings'
import { plugins } from './plugins'
import { defaultLexical } from '@/fields/defaultLexical'
import { getServerSideURL } from './utilities/getURL'

const filename = fileURLToPath(import.meta.url)
const dirname = path.dirname(filename)

export default buildConfig({
  admin: {
    components: {
      // The `BeforeLogin` component renders a message that you see while logging into your admin panel.
      beforeLogin: ['@/components/BeforeLogin', '@/components/RegistrationLink'],
      // Custom login logo
      graphics: {
        Logo: '@/components/LoginLogo',
        Icon: '@/components/AdminLogo',
      },
      // Dashboard components with role-based rendering
      beforeDashboard: ['@/components/CustomerDashboard'],
      // Customer profile view for better UX
      afterDashboard: ['@/components/CustomerProfileView'],
      // Custom navigation filter for customer users
      beforeNavLinks: ['@/components/CustomerNav'],
      afterNavLinks: [
        '@/components/AdminNavFilter',
        '@/components/CustomerProfileRedirect',
        '@/components/AdminAccessGuard',
      ],
    },
    importMap: {
      baseDir: path.resolve(dirname),
    },
    user: Users.slug,
    livePreview: {
      breakpoints: [
        {
          label: 'Mobile',
          name: 'mobile',
          width: 375,
          height: 667,
        },
        {
          label: 'Tablet',
          name: 'tablet',
          width: 768,
          height: 1024,
        },
        {
          label: 'Desktop',
          name: 'desktop',
          width: 1440,
          height: 900,
        },
      ],
    },
  },
  // This config helps us configure global or default features that the other editors can inherit
  editor: defaultLexical,
  db: mongooseAdapter({
    url: process.env.DATABASE_URI || '',
  }),
  collections: [
    Pages,
    Posts,
    Media,
    Categories,
    Users,
    Products,
    ProductCategories,
    CustomTshirts,
    SolidTshirts,
    Customers,
    Orders,
    MarketingExpenses,
    Testimonials,
    Partners,
    RecentWork,
  ],
  cors: [getServerSideURL()].filter(Boolean),
  globals: [Header, Footer, DTFPricing, SiteSettings],
  plugins: [...plugins],
  secret: process.env.PAYLOAD_SECRET,
  sharp,
  typescript: {
    outputFile: path.resolve(dirname, 'payload-types.ts'),
  },
  jobs: {
    access: {
      run: ({ req }: { req: PayloadRequest }): boolean => {
        // Allow logged in users to execute this endpoint (default)
        if (req.user) return true

        // If there is no logged in user, then check
        // for the Vercel Cron secret to be present as an
        // Authorization header:
        const authHeader = req.headers.get('authorization')
        return authHeader === `Bearer ${process.env.CRON_SECRET}`
      },
    },
    tasks: [],
  },
})
