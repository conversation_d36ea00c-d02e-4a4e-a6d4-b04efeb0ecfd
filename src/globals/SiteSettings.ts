import type { GlobalConfig } from 'payload'
import { adminOnly } from '../access/adminOnly'

export const SiteSettings: GlobalConfig = {
  slug: 'site-settings',
  access: {
    read: ({ req: { user } }) => {
      // Allow admin users to read site settings
      return Boolean(user && user.role === 'admin')
    },
    update: adminOnly,
  },
  admin: {
    group: 'Site Configuration',
    description: 'Manage site-wide settings including logos, branding, and general configuration',
  },
  versions: {
    max: 20,
    drafts: false,
  },
  fields: [
    {
      name: 'siteName',
      type: 'text',
      defaultValue: 'Maddox Tees',
      required: true,
      admin: {
        description: 'The name of your site/brand',
      },
    },
    {
      name: 'siteDescription',
      type: 'textarea',
      defaultValue: 'Premium B2B T-shirt printing and DTF services',
      admin: {
        description: 'Brief description of your site/business',
      },
    },
    {
      name: 'branding',
      type: 'group',
      label: 'Branding & Logos',
      fields: [
        {
          name: 'logo',
          type: 'upload',
          relationTo: 'media',
          required: false,
          admin: {
            description: 'Main site logo (recommended: SVG or PNG, max 300px width)',
          },
          filterOptions: {
            mimeType: { contains: 'image' },
          },
        },
        {
          name: 'logoAlt',
          type: 'text',
          defaultValue: 'Maddox Tees Logo',
          admin: {
            description: 'Alt text for the logo (for accessibility)',
          },
        },
        {
          name: 'logoWidth',
          type: 'number',
          defaultValue: 193,
          min: 50,
          max: 500,
          admin: {
            description: 'Logo width in pixels (default: 193px)',
          },
        },
        {
          name: 'logoHeight',
          type: 'number',
          defaultValue: 34,
          min: 20,
          max: 200,
          admin: {
            description: 'Logo height in pixels (default: 34px)',
          },
        },
        {
          name: 'favicon',
          type: 'upload',
          relationTo: 'media',
          required: false,
          admin: {
            description: 'Site favicon (recommended: ICO or PNG, 32x32px)',
          },
          filterOptions: {
            mimeType: { contains: 'image' },
          },
        },
      ],
    },
    {
      name: 'contact',
      type: 'group',
      label: 'Contact Information',
      fields: [
        {
          name: 'email',
          type: 'email',
          admin: {
            description: 'Primary contact email',
          },
        },
        {
          name: 'phone',
          type: 'text',
          admin: {
            description: 'Primary contact phone number',
          },
        },
        {
          name: 'address',
          type: 'textarea',
          admin: {
            description: 'Business address',
          },
        },
      ],
    },
    {
      name: 'social',
      type: 'group',
      label: 'Social Media',
      fields: [
        {
          name: 'facebook',
          type: 'text',
          admin: {
            description: 'Facebook page URL',
          },
        },
        {
          name: 'instagram',
          type: 'text',
          admin: {
            description: 'Instagram profile URL',
          },
        },
        {
          name: 'twitter',
          type: 'text',
          admin: {
            description: 'Twitter/X profile URL',
          },
        },
        {
          name: 'linkedin',
          type: 'text',
          admin: {
            description: 'LinkedIn company page URL',
          },
        },
      ],
    },
    {
      name: 'seo',
      type: 'group',
      label: 'SEO Settings',
      fields: [
        {
          name: 'metaTitle',
          type: 'text',
          admin: {
            description: 'Default meta title for pages without specific SEO settings',
          },
        },
        {
          name: 'metaDescription',
          type: 'textarea',
          admin: {
            description: 'Default meta description for pages without specific SEO settings',
          },
        },
        {
          name: 'ogImage',
          type: 'upload',
          relationTo: 'media',
          required: false,
          admin: {
            description: 'Default Open Graph image (recommended: 1200x630px)',
          },
          filterOptions: {
            mimeType: { contains: 'image' },
          },
        },
      ],
    },
    {
      name: 'lastUpdated',
      type: 'date',
      admin: {
        readOnly: true,
        description: 'Last time settings were updated',
        position: 'sidebar',
      },
    },
    {
      name: 'updatedBy',
      type: 'relationship',
      relationTo: 'users',
      admin: {
        readOnly: true,
        description: 'Admin who last updated settings',
        position: 'sidebar',
      },
    },
  ],
  hooks: {
    beforeChange: [
      ({ data, req }) => {
        // Auto-update timestamp and user
        data.lastUpdated = new Date().toISOString()
        if (req.user) {
          data.updatedBy = req.user.id
        }
        return data
      },
    ],
    afterChange: [
      ({ doc, req }) => {
        // Log settings changes for audit trail
        if (req.user) {
          console.log(`Site Settings updated by ${req.user.email} at ${new Date().toISOString()}`)
        }
      },
    ],
  },
}
