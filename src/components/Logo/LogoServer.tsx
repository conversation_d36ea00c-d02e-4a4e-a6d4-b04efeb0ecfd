import clsx from 'clsx'
import React from 'react'
import { getLogoServer } from '@/utilities/getSiteSettings'

interface Props {
  className?: string
  loading?: 'lazy' | 'eager'
  priority?: 'auto' | 'high' | 'low'
}

export const LogoServer = async (props: Props) => {
  const { loading: loadingFromProps, priority: priorityFromProps, className } = props

  const loading = loadingFromProps || 'lazy'
  const priority = priorityFromProps || 'low'

  // Get logo information from server-side utility
  const { src: logoSrc, alt: altText, width, height, hasCustomLogo } = await getLogoServer()

  // Debug logging
  console.log('LogoServer: Logo loaded -', hasCustomLogo ? 'Custom logo' : 'Default logo')
  if (hasCustomLogo) {
    console.log('LogoServer: Custom logo URL:', logoSrc)
  }

  return (
    /* eslint-disable @next/next/no-img-element */
    <img
      alt={altText}
      width={width}
      height={height}
      loading={loading}
      fetchPriority={priority}
      decoding="async"
      className={clsx('max-w-[9.375rem] w-full', className)}
      style={{ height: `${height}px` }}
      src={logoSrc}
    />
  )
}
