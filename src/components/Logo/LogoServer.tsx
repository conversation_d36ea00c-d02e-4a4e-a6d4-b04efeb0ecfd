import clsx from 'clsx'
import React from 'react'
import { getSiteSettings } from '@/hooks/useSiteSettings'

interface Props {
  className?: string
  loading?: 'lazy' | 'eager'
  priority?: 'auto' | 'high' | 'low'
}

export const LogoServer = async (props: Props) => {
  const { loading: loadingFromProps, priority: priorityFromProps, className } = props
  
  // Fetch site settings on the server
  const siteSettings = await getSiteSettings()

  const loading = loadingFromProps || 'lazy'
  const priority = priorityFromProps || 'low'

  // Use custom logo if available, otherwise fallback to default Payload logo
  const logo = siteSettings?.branding?.logo
  const logoSrc = logo?.url || "https://raw.githubusercontent.com/payloadcms/payload/main/packages/ui/src/assets/payload-logo-light.svg"
  const altText = logo?.alt || siteSettings?.branding?.logoAlt || "Maddox Tees Logo"
  const width = logo?.width || siteSettings?.branding?.logoWidth || 193
  const height = logo?.height || siteSettings?.branding?.logoHeight || 34

  return (
    /* eslint-disable @next/next/no-img-element */
    <img
      alt={altText}
      width={width}
      height={height}
      loading={loading}
      fetchPriority={priority}
      decoding="async"
      className={clsx('max-w-[9.375rem] w-full', className)}
      style={{ height: `${height}px` }}
      src={logoSrc}
    />
  )
}
