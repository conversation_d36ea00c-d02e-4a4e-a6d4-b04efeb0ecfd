import clsx from 'clsx'
import React from 'react'
import { useSiteSettings } from '@/hooks/useSiteSettings'

interface Props {
  className?: string
  loading?: 'lazy' | 'eager'
  priority?: 'auto' | 'high' | 'low'
}

export const Logo = (props: Props) => {
  const { loading: loadingFromProps, priority: priorityFromProps, className } = props
  const { logo, logoAlt, logoWidth, logoHeight, loading: settingsLoading } = useSiteSettings()

  const loading = loadingFromProps || 'lazy'
  const priority = priorityFromProps || 'low'

  // Use custom logo if available, otherwise fallback to default Payload logo
  const logoSrc =
    logo?.url ||
    'https://raw.githubusercontent.com/payloadcms/payload/main/packages/ui/src/assets/payload-logo-light.svg'
  const altText = logo?.alt || logoAlt || 'Maddox Tees Logo'
  const width = logo?.width || logoWidth || 193
  const height = logo?.height || logoHeight || 34

  // Show loading state or fallback logo while settings are loading
  if (settingsLoading) {
    return (
      /* eslint-disable @next/next/no-img-element */
      <img
        alt="Loading..."
        width={193}
        height={34}
        loading={loading}
        fetchPriority={priority}
        decoding="async"
        className={clsx('max-w-[9.375rem] w-full h-[34px] opacity-50', className)}
        src="https://raw.githubusercontent.com/payloadcms/payload/main/packages/ui/src/assets/payload-logo-light.svg"
      />
    )
  }

  return (
    /* eslint-disable @next/next/no-img-element */
    <img
      alt={altText}
      width={width}
      height={height}
      loading={loading}
      fetchPriority={priority}
      decoding="async"
      className={clsx('max-w-[9.375rem] w-full', className)}
      style={{ height: `${height}px` }}
      src={logoSrc}
    />
  )
}
