'use client'

import React, { useEffect, useState } from 'react'

interface SiteSettingsData {
  branding?: {
    logo?: {
      url: string
      alt?: string
      width?: number
      height?: number
    }
    logoAlt?: string
    logoWidth?: number
    logoHeight?: number
  }
  siteName?: string
}

export const AdminLogo: React.FC = () => {
  const [siteSettings, setSiteSettings] = useState<SiteSettingsData | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchSiteSettings = async () => {
      try {
        // Try public logo API first, fallback to main API
        let response = await fetch('/api/public/logo')
        if (!response.ok) {
          response = await fetch('/api/site-settings')
        }

        if (response.ok) {
          const data = await response.json()
          setSiteSettings(data)
        }
      } catch (error) {
        console.error('Error fetching site settings for admin logo:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchSiteSettings()
  }, [])

  // Show loading state
  if (loading) {
    return <span>Loading...</span>
  }

  // Use site name if available
  const siteName = siteSettings?.siteName || 'Dashboard'

  // Use custom logo if available (small version for admin header)
  const logo = siteSettings?.branding?.logo
  if (logo?.url) {
    return (
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        <img
          src={logo.url}
          alt={logo.alt || siteSettings?.branding?.logoAlt || siteName}
          style={{
            maxWidth: '24px',
            maxHeight: '24px',
            objectFit: 'contain',
          }}
        />
        <span>{siteName}</span>
      </div>
    )
  }

  // Fallback to site name only
  return <span>{siteName}</span>
}

export default AdminLogo
