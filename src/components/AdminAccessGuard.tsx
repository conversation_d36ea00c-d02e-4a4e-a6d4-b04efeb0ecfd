'use client'

import { useAuth } from '@payloadcms/ui'
import { useRouter, usePathname } from 'next/navigation'
import { useEffect, useState } from 'react'

/**
 * Admin Access Guard Component
 *
 * This component redirects customer users away from admin-only collection pages
 * when they try to access them directly via URL manipulation.
 */
export const AdminAccessGuard: React.FC = () => {
  const { user } = useAuth()
  const router = useRouter()
  const pathname = usePathname()
  const [hasRedirected, setHasRedirected] = useState(false)

  useEffect(() => {
    // Only run this check for authenticated users
    if (!user) return

    // Only check for customer users (admin users should have full access)
    if (user.role !== 'customer') return

    // Prevent multiple redirects
    if (hasRedirected) return

    // Define admin-only collection routes that customers should not access
    const adminOnlyRoutes = [
      '/admin/collections/media',
      '/admin/collections/products',
      '/admin/collections/product-categories',
      '/admin/collections/categories',
      '/admin/collections/posts',
      '/admin/collections/pages',
      '/admin/collections/testimonials',
      '/admin/collections/partners',
      '/admin/collections/recent-works',
      '/admin/collections/custom-tshirts',
      '/admin/collections/solid-tshirts',
      '/admin/collections/marketing-expenses',
      '/admin/collections/forms',
      '/admin/collections/form-submissions',
      '/admin/collections/search-results',
      '/admin/collections/redirects',
      '/admin/collections/customers', // Customers should use portal, not direct access
      '/admin/collections/users', // Users management is admin-only
      '/admin/globals/header',
      '/admin/globals/footer',
      '/admin/globals/dtf-pricing',
    ]

    // Check if current path matches any admin-only route
    const isAdminOnlyRoute = adminOnlyRoutes.some((route) => pathname.startsWith(route))

    // Special case: Allow customers to access their own customer record
    // Extract the actual customer ID from the customerId field
    let customerIdString: string | null = null
    if (user.customerId) {
      if (typeof user.customerId === 'string') {
        customerIdString = user.customerId
      } else if (
        user.customerId &&
        typeof user.customerId === 'object' &&
        'id' in user.customerId
      ) {
        customerIdString = (user.customerId as any).id
      }
    }

    const isOwnCustomerRecord =
      customerIdString && pathname === `/admin/collections/customers/${customerIdString}`

    if (isAdminOnlyRoute && !isOwnCustomerRecord) {
      console.log('🚫 Customer user attempting to access admin-only route:', pathname)
      console.log('👤 User details:', { id: user.id, email: user.email, role: user.role })

      setHasRedirected(true)

      // Show immediate feedback
      alert(
        '⚠️ Access Denied\n\nThis section is only available to administrators.\n\nYou will be redirected to your customer dashboard.',
      )

      // Redirect customer users to their dashboard
      setTimeout(() => {
        router.push('/admin')
      }, 100)
    }
  }, [user, pathname, router, hasRedirected])

  // Reset redirect flag when pathname changes to a non-restricted route
  useEffect(() => {
    if (user?.role === 'customer' && pathname === '/admin') {
      setHasRedirected(false)
    }
  }, [pathname, user])

  // This component doesn't render anything visible
  return null
}

export default AdminAccessGuard
