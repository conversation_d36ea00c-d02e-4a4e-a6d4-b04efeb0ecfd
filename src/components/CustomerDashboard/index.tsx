'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { useAuth } from '@payloadcms/ui'
import { useRouter } from 'next/navigation'

const CustomerDashboard: React.FC = () => {
  const { user } = useAuth()
  const router = useRouter()
  const [isLoadingProfile, setIsLoadingProfile] = useState(false)

  // Only show for customer users
  if (!user || user.role !== 'customer') {
    return null
  }

  // Handle profile management navigation
  const handleManageProfile = async () => {
    if (!user.customerId) {
      alert('❌ Error: No customer profile found. Please contact support.')
      return
    }

    setIsLoadingProfile(true)

    try {
      // Extract the actual customer ID from the customerId field
      // customerId might be an object with an id property, or just a string
      let customerIdString: string

      if (typeof user.customerId === 'string') {
        customerIdString = user.customerId
      } else if (
        user.customerId &&
        typeof user.customerId === 'object' &&
        'id' in user.customerId
      ) {
        customerIdString = (user.customerId as any).id
      } else {
        console.error('Invalid customerId format:', user.customerId)
        alert('❌ Error: Invalid customer profile format. Please contact support.')
        return
      }

      console.log('🔍 Navigating to customer profile:', customerIdString)

      // Navigate directly to the customer's edit page
      router.push(`/admin/collections/customers/${customerIdString}`)
    } catch (error) {
      console.error('Error navigating to profile:', error)
      alert('❌ Error loading profile. Please try again.')
    } finally {
      setIsLoadingProfile(false)
    }
  }

  return (
    <div className="customer-dashboard">
      <style jsx>{`
        .customer-dashboard {
          max-width: 800px;
          margin: 0 auto;
          padding: 2rem;
        }
        .dashboard-header {
          text-align: center;
          margin-bottom: 2rem;
        }
        .dashboard-title {
          font-size: 2rem;
          font-weight: 700;
          color: #212529;
          margin-bottom: 0.5rem;
        }
        .dashboard-subtitle {
          font-size: 1.1rem;
          color: #6c757d;
        }
        .dashboard-content {
          background: #fff;
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 2rem;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .info-section {
          margin-bottom: 1.5rem;
        }
        .info-title {
          font-size: 1.2rem;
          font-weight: 600;
          color: #495057;
          margin-bottom: 0.5rem;
        }
        .info-text {
          color: #6c757d;
          line-height: 1.6;
        }
        .orders-link {
          display: inline-block;
          background: #007bff;
          color: white;
          padding: 0.75rem 1.5rem;
          border-radius: 4px;
          text-decoration: none;
          font-weight: 500;
          transition: background-color 0.2s;
        }
        .orders-link:hover {
          background: #0056b3;
          color: white;
        }
      `}</style>

      <div className="dashboard-header">
        <h1 className="dashboard-title">Customer Portal</h1>
        <p className="dashboard-subtitle">Welcome, {user.name || user.email}</p>
      </div>

      <div className="dashboard-content">
        <div className="info-section">
          <h2 className="info-title">Your Order History</h2>
          <p className="info-text">
            View and track all your orders with Maddox Tees. You can see order status, tracking
            information, and download invoices for completed orders.
          </p>
          <Link href="/admin/collections/orders" className="orders-link">
            View My Orders
          </Link>
        </div>

        <div className="info-section">
          <h2 className="info-title">My Profile</h2>
          <p className="info-text">
            Manage your company information, billing address, and shipping address. Keep your
            contact details up to date for smooth order processing.
          </p>
          <button
            onClick={handleManageProfile}
            className="orders-link"
            disabled={isLoadingProfile}
            style={{
              border: 'none',
              cursor: isLoadingProfile ? 'not-allowed' : 'pointer',
              opacity: isLoadingProfile ? 0.7 : 1,
            }}
          >
            {isLoadingProfile ? 'Loading Profile...' : 'Manage My Profile'}
          </button>
        </div>

        <div className="info-section">
          <h2 className="info-title">Need Help?</h2>
          <p className="info-text">
            If you have any questions about your orders or need assistance, please contact our
            customer support team.
          </p>
        </div>
      </div>
    </div>
  )
}

export default CustomerDashboard
