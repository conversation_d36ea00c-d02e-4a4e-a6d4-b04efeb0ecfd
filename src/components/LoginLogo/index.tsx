'use client'

import React, { useEffect, useState } from 'react'

interface SiteSettingsData {
  branding?: {
    logo?: {
      url: string
      alt?: string
      width?: number
      height?: number
    }
    logoAlt?: string
    logoWidth?: number
    logoHeight?: number
  }
  siteName?: string
}

const LoginLogo: React.FC = () => {
  const [siteSettings, setSiteSettings] = useState<SiteSettingsData | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchSiteSettings = async () => {
      try {
        // Try public logo API first, fallback to main API
        let response = await fetch('/api/public/logo')
        if (!response.ok) {
          response = await fetch('/api/site-settings')
        }

        if (response.ok) {
          const data = await response.json()
          setSiteSettings(data)
        }
      } catch (error) {
        console.error('Error fetching site settings for login logo:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchSiteSettings()
  }, [])

  // Show loading state
  if (loading) {
    return (
      <div className="login__brand">
        <div
          style={{ width: '193px', height: '43.5px', background: '#f0f0f0', borderRadius: '4px' }}
        />
      </div>
    )
  }

  // Use custom logo if available
  const logo = siteSettings?.branding?.logo
  if (logo?.url) {
    const width = logo.width || siteSettings?.branding?.logoWidth || 193
    const height = logo.height || siteSettings?.branding?.logoHeight || 43.5
    const alt = logo.alt || siteSettings?.branding?.logoAlt || siteSettings?.siteName || 'Logo'

    return (
      <div className="login__brand">
        <img
          src={logo.url}
          alt={alt}
          width={width}
          height={height}
          style={{
            maxWidth: '193px',
            maxHeight: '43.5px',
            objectFit: 'contain',
          }}
        />
      </div>
    )
  }

  // Fallback to default Payload logo
  return (
    <div className="login__brand">
      <svg
        className="graphic-logo"
        fill="none"
        height="43.5"
        viewBox="0 0 193.38 43.5"
        width="193.38"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M121.149 34.325L61.8294 0.225C61.3275 -0.075 60.6584 -0.075 60.1565 0.225L28.8069 18.2583C28.1378 18.6583 28.1378 19.5917 28.8069 19.9917L44.1973 28.8583C44.833 29.225 45.5691 29.225 46.2048 28.8583L60.2569 20.7917C60.7588 20.4917 61.4279 20.4917 61.9298 20.7917L103.283 44.5583C103.785 44.8583 104.12 45.3917 104.12 45.9917V82.8917C104.12 83.5917 104.488 84.2583 105.123 84.625L120.514 93.4583C121.183 93.8583 122.019 93.3583 122.019 92.5917V35.7917C122.019 35.1917 121.685 34.6583 121.183 34.3583L121.149 34.325Z"
          fill="currentColor"
        />
        <path
          d="M0 35.7917V92.5917C0 93.3583 0.836 93.8583 1.505 93.4583L16.896 84.625C17.531 84.2583 17.899 83.5917 17.899 82.8917V45.9917C17.899 45.3917 18.234 44.8583 18.736 44.5583L60.089 20.7917C60.591 20.4917 61.26 20.4917 61.762 20.7917L75.814 28.8583C76.45 29.225 77.186 29.225 77.822 28.8583L93.212 19.9917C93.881 19.5917 93.881 18.6583 93.212 18.2583L61.863 0.225C61.361 -0.075 60.692 -0.075 60.19 0.225L0.87 34.325C0.368 34.6583 0.034 35.1917 0.034 35.7917H0Z"
          fill="currentColor"
        />
      </svg>
    </div>
  )
}

export default LoginLogo
