import type { CollectionConfig } from 'payload'

import { authenticated } from '../access/authenticated'
import { authenticatedOrPublished } from '../access/authenticatedOrPublished'
import { adminOnly } from '../access/adminOnly'
import { slugField } from '@/fields/slug'

export const ProductCategories: CollectionConfig<'product-categories'> = {
  slug: 'product-categories',
  access: {
    admin: ({ req: { user } }) => {
      // Only show in admin panel for admin users
      return Boolean(user && user.role === 'admin')
    },
    create: adminOnly,
    delete: adminOnly,
    read: adminOnly,
    update: adminOnly,
  },
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'slug', 'updatedAt'],
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
    },
    {
      name: 'description',
      type: 'textarea',
    },
    {
      name: 'image',
      type: 'upload',
      relationTo: 'media',
      admin: {
        description: 'Category image for display purposes',
      },
    },
    {
      name: 'parent',
      type: 'relationship',
      relationTo: 'product-categories',
      admin: {
        description: 'Parent category (if this is a subcategory)',
      },
    },
    {
      name: 'featured',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        position: 'sidebar',
        description: 'Feature this category on the home page',
      },
    },
    ...slugField(),
  ],
}
