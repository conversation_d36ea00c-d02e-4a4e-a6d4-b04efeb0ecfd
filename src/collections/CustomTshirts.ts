import type { CollectionConfig } from 'payload'
import { adminOnly } from '../access/adminOnly'

export const CustomTshirts: CollectionConfig = {
  slug: 'custom-tshirts',
  labels: {
    singular: 'Custom T-shirt',
    plural: 'Custom T-shirts',
  },
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'basePrice', 'status', 'updatedAt'],
    group: 'Products',
  },
  access: {
    admin: ({ req: { user } }) => {
      // Only show in admin panel for admin users
      return Boolean(user && user.role === 'admin')
    },
    read: adminOnly,
    create: adminOnly,
    update: adminOnly,
    delete: adminOnly,
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      label: 'Product Name',
    },
    {
      name: 'description',
      type: 'textarea',
      label: 'Description',
    },
    {
      name: 'basePrice',
      type: 'number',
      required: true,
      label: 'Base Price (₹)',
      min: 0,
    },
    {
      name: 'materials',
      type: 'array',
      label: 'Available Materials',
      minRows: 1,
      fields: [
        {
          name: 'material',
          type: 'select',
          required: true,
          options: [
            { label: 'Cotton', value: 'cotton' },
            { label: 'Polyester', value: 'polyester' },
            { label: 'Poly-Cotton Blend', value: 'poly-cotton' },
            { label: 'Organic Cotton', value: 'organic-cotton' },
            { label: 'Bamboo', value: 'bamboo' },
            { label: 'Modal', value: 'modal' },
          ],
        },
        {
          name: 'priceModifier',
          type: 'number',
          label: 'Price Modifier (₹)',
          defaultValue: 0,
        },
      ],
    },
    {
      name: 'sizes',
      type: 'array',
      label: 'Available Sizes',
      minRows: 1,
      fields: [
        {
          name: 'size',
          type: 'select',
          required: true,
          options: [
            { label: 'XS', value: 'xs' },
            { label: 'S', value: 's' },
            { label: 'M', value: 'm' },
            { label: 'L', value: 'l' },
            { label: 'XL', value: 'xl' },
            { label: 'XXL', value: 'xxl' },
            { label: 'XXXL', value: 'xxxl' },
          ],
        },
        {
          name: 'priceModifier',
          type: 'number',
          label: 'Price Modifier (₹)',
          defaultValue: 0,
        },
      ],
    },
    {
      name: 'colors',
      type: 'array',
      label: 'Available Colors',
      minRows: 1,
      fields: [
        {
          name: 'colorName',
          type: 'text',
          required: true,
          label: 'Color Name',
        },
        {
          name: 'colorCode',
          type: 'text',
          label: 'Color Code (Hex)',
          validate: (val: string | null | undefined) => {
            if (val && !/^#[0-9A-F]{6}$/i.test(val)) {
              return 'Please enter a valid hex color code (e.g., #FF0000)'
            }
            return true
          },
        },
        {
          name: 'priceModifier',
          type: 'number',
          label: 'Price Modifier (₹)',
          defaultValue: 0,
        },
      ],
    },
    {
      name: 'styles',
      type: 'array',
      label: 'Available Styles',
      minRows: 1,
      fields: [
        {
          name: 'styleName',
          type: 'select',
          required: true,
          options: [
            { label: 'Round Neck', value: 'round-neck' },
            { label: 'V-Neck', value: 'v-neck' },
            { label: 'Polo', value: 'polo' },
            { label: 'Hoodie', value: 'hoodie' },
            { label: 'Tank Top', value: 'tank-top' },
            { label: 'Long Sleeve', value: 'long-sleeve' },
          ],
        },
        {
          name: 'priceModifier',
          type: 'number',
          label: 'Price Modifier (₹)',
          defaultValue: 0,
        },
      ],
    },
    {
      name: 'images',
      type: 'array',
      label: 'Product Images',
      fields: [
        {
          name: 'image',
          type: 'upload',
          relationTo: 'media',
          required: true,
        },
        {
          name: 'alt',
          type: 'text',
          label: 'Alt Text',
        },
        {
          name: 'isPrimary',
          type: 'checkbox',
          label: 'Primary Image',
          defaultValue: false,
        },
      ],
    },
    {
      name: 'printPlacements',
      type: 'array',
      label: 'Available Print Placements',
      defaultValue: [
        { placement: 'front', priceModifier: 0 },
        { placement: 'back', priceModifier: 50 },
        { placement: 'left-sleeve', priceModifier: 30 },
        { placement: 'right-sleeve', priceModifier: 30 },
      ],
      fields: [
        {
          name: 'placement',
          type: 'select',
          required: true,
          options: [
            { label: 'Front', value: 'front' },
            { label: 'Back', value: 'back' },
            { label: 'Left Sleeve', value: 'left-sleeve' },
            { label: 'Right Sleeve', value: 'right-sleeve' },
            { label: 'Front & Back', value: 'front-back' },
          ],
        },
        {
          name: 'priceModifier',
          type: 'number',
          label: 'Price Modifier (₹)',
          defaultValue: 0,
        },
      ],
    },
    {
      name: 'minimumQuantity',
      type: 'number',
      label: 'Minimum Order Quantity',
      defaultValue: 1,
      min: 1,
    },
    {
      name: 'maximumQuantity',
      type: 'number',
      label: 'Maximum Order Quantity',
      defaultValue: 1000,
      min: 1,
    },
    {
      name: 'processingTime',
      type: 'text',
      label: 'Processing Time',
      defaultValue: '5-7 business days',
    },
    {
      name: 'status',
      type: 'select',
      required: true,
      defaultValue: 'draft',
      options: [
        { label: 'Draft', value: 'draft' },
        { label: 'Published', value: 'published' },
        { label: 'Archived', value: 'archived' },
      ],
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'featured',
      type: 'checkbox',
      label: 'Featured Product',
      defaultValue: false,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'tags',
      type: 'array',
      label: 'Tags',
      fields: [
        {
          name: 'tag',
          type: 'text',
          required: true,
        },
      ],
      admin: {
        position: 'sidebar',
      },
    },
  ],
  hooks: {
    beforeChange: [
      ({ data }) => {
        // Ensure only one primary image
        if (data.images && Array.isArray(data.images)) {
          let primaryCount = 0
          data.images.forEach((img: { isPrimary?: boolean }, index: number) => {
            if (img.isPrimary) {
              primaryCount++
              if (primaryCount > 1) {
                data.images[index].isPrimary = false
              }
            }
          })

          // If no primary image, make the first one primary
          if (primaryCount === 0 && data.images.length > 0) {
            data.images[0].isPrimary = true
          }
        }

        return data
      },
    ],
  },
}
