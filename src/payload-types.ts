/* tslint:disable */
/* eslint-disable */
/**
 * This file was automatically generated by Payload.
 * DO NOT MODIFY IT BY HAND. Instead, modify your source Payload config,
 * and re-run `payload generate:types` to regenerate this file.
 */

/**
 * Supported timezones in IANA format.
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "supportedTimezones".
 */
export type SupportedTimezones =
  | 'Pacific/Midway'
  | 'Pacific/Niue'
  | 'Pacific/Honolulu'
  | 'Pacific/Rarotonga'
  | 'America/Anchorage'
  | 'Pacific/Gambier'
  | 'America/Los_Angeles'
  | 'America/Tijuana'
  | 'America/Denver'
  | 'America/Phoenix'
  | 'America/Chicago'
  | 'America/Guatemala'
  | 'America/New_York'
  | 'America/Bogota'
  | 'America/Caracas'
  | 'America/Santiago'
  | 'America/Buenos_Aires'
  | 'America/Sao_Paulo'
  | 'Atlantic/South_Georgia'
  | 'Atlantic/Azores'
  | 'Atlantic/Cape_Verde'
  | 'Europe/London'
  | 'Europe/Berlin'
  | 'Africa/Lagos'
  | 'Europe/Athens'
  | 'Africa/Cairo'
  | 'Europe/Moscow'
  | 'Asia/Riyadh'
  | 'Asia/Dubai'
  | 'Asia/Baku'
  | 'Asia/Karachi'
  | 'Asia/Tashkent'
  | 'Asia/Calcutta'
  | 'Asia/Dhaka'
  | 'Asia/Almaty'
  | 'Asia/Jakarta'
  | 'Asia/Bangkok'
  | 'Asia/Shanghai'
  | 'Asia/Singapore'
  | 'Asia/Tokyo'
  | 'Asia/Seoul'
  | 'Australia/Brisbane'
  | 'Australia/Sydney'
  | 'Pacific/Guam'
  | 'Pacific/Noumea'
  | 'Pacific/Auckland'
  | 'Pacific/Fiji';

export interface Config {
  auth: {
    users: UserAuthOperations;
  };
  blocks: {};
  collections: {
    pages: Page;
    posts: Post;
    media: Media;
    categories: Category;
    users: User;
    products: Product;
    'product-categories': ProductCategory;
    'custom-tshirts': CustomTshirt;
    'solid-tshirts': SolidTshirt;
    customers: Customer;
    orders: Order;
    'marketing-expenses': MarketingExpense;
    testimonials: Testimonial;
    partners: Partner;
    'recent-work': RecentWork;
    redirects: Redirect;
    forms: Form;
    'form-submissions': FormSubmission;
    search: Search;
    'payload-jobs': PayloadJob;
    'payload-locked-documents': PayloadLockedDocument;
    'payload-preferences': PayloadPreference;
    'payload-migrations': PayloadMigration;
  };
  collectionsJoins: {};
  collectionsSelect: {
    pages: PagesSelect<false> | PagesSelect<true>;
    posts: PostsSelect<false> | PostsSelect<true>;
    media: MediaSelect<false> | MediaSelect<true>;
    categories: CategoriesSelect<false> | CategoriesSelect<true>;
    users: UsersSelect<false> | UsersSelect<true>;
    products: ProductsSelect<false> | ProductsSelect<true>;
    'product-categories': ProductCategoriesSelect<false> | ProductCategoriesSelect<true>;
    'custom-tshirts': CustomTshirtsSelect<false> | CustomTshirtsSelect<true>;
    'solid-tshirts': SolidTshirtsSelect<false> | SolidTshirtsSelect<true>;
    customers: CustomersSelect<false> | CustomersSelect<true>;
    orders: OrdersSelect<false> | OrdersSelect<true>;
    'marketing-expenses': MarketingExpensesSelect<false> | MarketingExpensesSelect<true>;
    testimonials: TestimonialsSelect<false> | TestimonialsSelect<true>;
    partners: PartnersSelect<false> | PartnersSelect<true>;
    'recent-work': RecentWorkSelect<false> | RecentWorkSelect<true>;
    redirects: RedirectsSelect<false> | RedirectsSelect<true>;
    forms: FormsSelect<false> | FormsSelect<true>;
    'form-submissions': FormSubmissionsSelect<false> | FormSubmissionsSelect<true>;
    search: SearchSelect<false> | SearchSelect<true>;
    'payload-jobs': PayloadJobsSelect<false> | PayloadJobsSelect<true>;
    'payload-locked-documents': PayloadLockedDocumentsSelect<false> | PayloadLockedDocumentsSelect<true>;
    'payload-preferences': PayloadPreferencesSelect<false> | PayloadPreferencesSelect<true>;
    'payload-migrations': PayloadMigrationsSelect<false> | PayloadMigrationsSelect<true>;
  };
  db: {
    defaultIDType: string;
  };
  globals: {
    header: Header;
    footer: Footer;
    'dtf-pricing': DtfPricing;
    'site-settings': SiteSetting;
  };
  globalsSelect: {
    header: HeaderSelect<false> | HeaderSelect<true>;
    footer: FooterSelect<false> | FooterSelect<true>;
    'dtf-pricing': DtfPricingSelect<false> | DtfPricingSelect<true>;
    'site-settings': SiteSettingsSelect<false> | SiteSettingsSelect<true>;
  };
  locale: null;
  user: User & {
    collection: 'users';
  };
  jobs: {
    tasks: {
      schedulePublish: TaskSchedulePublish;
      inline: {
        input: unknown;
        output: unknown;
      };
    };
    workflows: unknown;
  };
}
export interface UserAuthOperations {
  forgotPassword: {
    email: string;
    password: string;
  };
  login: {
    email: string;
    password: string;
  };
  registerFirstUser: {
    email: string;
    password: string;
  };
  unlock: {
    email: string;
    password: string;
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "pages".
 */
export interface Page {
  id: string;
  title: string;
  hero: {
    type: 'none' | 'highImpact' | 'mediumImpact' | 'lowImpact';
    richText?: {
      root: {
        type: string;
        children: {
          type: string;
          version: number;
          [k: string]: unknown;
        }[];
        direction: ('ltr' | 'rtl') | null;
        format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
        indent: number;
        version: number;
      };
      [k: string]: unknown;
    } | null;
    links?:
      | {
          link: {
            type?: ('reference' | 'custom') | null;
            newTab?: boolean | null;
            reference?:
              | ({
                  relationTo: 'pages';
                  value: string | Page;
                } | null)
              | ({
                  relationTo: 'posts';
                  value: string | Post;
                } | null);
            url?: string | null;
            label: string;
            /**
             * Choose how the link should be rendered.
             */
            appearance?: ('default' | 'outline') | null;
          };
          id?: string | null;
        }[]
      | null;
    media?: (string | null) | Media;
  };
  layout: (
    | CallToActionBlock
    | ContentBlock
    | MediaBlock
    | ArchiveBlock
    | FormBlock
    | {
        title: string;
        subtitle?: string | null;
        content?: {
          root: {
            type: string;
            children: {
              type: string;
              version: number;
              [k: string]: unknown;
            }[];
            direction: ('ltr' | 'rtl') | null;
            format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
            indent: number;
            version: number;
          };
          [k: string]: unknown;
        } | null;
        backgroundImage: string | Media;
        /**
         * Add a dark overlay to the background image for better text visibility
         */
        backgroundOverlay?: boolean | null;
        overlayOpacity?: ('0.1' | '0.2' | '0.3' | '0.4' | '0.5' | '0.6' | '0.7' | '0.8' | '0.9') | null;
        textColor?: ('light' | 'dark') | null;
        textAlignment?: ('left' | 'center' | 'right') | null;
        height?: ('small' | 'medium' | 'large' | 'full') | null;
        primaryCTA: {
          label: string;
          link: string;
          style?: ('primary' | 'secondary' | 'outline') | null;
          size?: ('sm' | 'md' | 'lg') | null;
        };
        secondaryCTA: {
          label: string;
          link: string;
          style?: ('primary' | 'secondary' | 'outline') | null;
          size?: ('sm' | 'md' | 'lg') | null;
        };
        id?: string | null;
        blockName?: string | null;
        blockType: 'hero-banner';
      }
    | {
        heading: string;
        subheading?: string | null;
        services?:
          | {
              title: string;
              description: string;
              image: string | Media;
              link: string;
              linkLabel?: string | null;
              id?: string | null;
            }[]
          | null;
        /**
         * Auto-rotate carousel items
         */
        autoRotate?: boolean | null;
        /**
         * Rotation interval in milliseconds
         */
        rotationInterval?: number | null;
        /**
         * Pause rotation on hover
         */
        pauseOnHover?: boolean | null;
        /**
         * Show navigation dots
         */
        showDots?: boolean | null;
        /**
         * Show navigation arrows
         */
        showArrows?: boolean | null;
        backgroundColor?: ('light' | 'dark' | 'primary' | 'secondary' | 'transparent') | null;
        textColor?: ('light' | 'dark') | null;
        padding?: ('none' | 'small' | 'medium' | 'large') | null;
        id?: string | null;
        blockName?: string | null;
        blockType: 'featured-services';
      }
    | {
        heading: string;
        subheading?: string | null;
        /**
         * Select testimonials to display
         */
        testimonials: (string | Testimonial)[];
        layout?: ('carousel' | 'grid' | 'list') | null;
        /**
         * Number of testimonials to display
         */
        displayCount?: number | null;
        /**
         * Show star ratings
         */
        showRating?: boolean | null;
        /**
         * Show company logos
         */
        showCompanyLogo?: boolean | null;
        /**
         * Show customer photos
         */
        showCustomerPhoto?: boolean | null;
        backgroundColor?: ('light' | 'dark' | 'primary' | 'secondary' | 'transparent') | null;
        textColor?: ('light' | 'dark') | null;
        padding?: ('none' | 'small' | 'medium' | 'large') | null;
        /**
         * Auto-rotate carousel items
         */
        autoRotate?: boolean | null;
        /**
         * Rotation interval in milliseconds
         */
        rotationInterval?: number | null;
        /**
         * Pause rotation on hover
         */
        pauseOnHover?: boolean | null;
        /**
         * Show navigation dots
         */
        showDots?: boolean | null;
        /**
         * Show navigation arrows
         */
        showArrows?: boolean | null;
        id?: string | null;
        blockName?: string | null;
        blockType: 'testimonials-block';
      }
    | {
        heading: string;
        subheading?: string | null;
        /**
         * Select projects to display
         */
        projects: (string | RecentWork)[];
        layout?: ('grid' | 'masonry' | 'carousel') | null;
        /**
         * Number of projects to display
         */
        displayCount?: number | null;
        /**
         * Enable category filtering
         */
        enableFiltering?: boolean | null;
        /**
         * Categories to include in filters
         */
        categories?:
          | ('dtf' | 'custom_tshirts' | 'corporate_uniforms' | 'event_merchandise' | 'promotional_products' | 'other')[]
          | null;
        /**
         * Show project title
         */
        showTitle?: boolean | null;
        /**
         * Show client name
         */
        showClient?: boolean | null;
        /**
         * Show project category
         */
        showCategory?: boolean | null;
        /**
         * Enable image lightbox on click
         */
        enableLightbox?: boolean | null;
        /**
         * Link to project detail page
         */
        linkToDetailPage?: boolean | null;
        backgroundColor?: ('light' | 'dark' | 'primary' | 'secondary' | 'transparent') | null;
        textColor?: ('light' | 'dark') | null;
        padding?: ('none' | 'small' | 'medium' | 'large') | null;
        viewAllButton?: {
          show?: boolean | null;
          label?: string | null;
          link?: string | null;
        };
        id?: string | null;
        blockName?: string | null;
        blockType: 'recent-work-block';
      }
    | {
        heading: string;
        subheading?: string | null;
        /**
         * Select partners to display
         */
        partners: (string | Partner)[];
        layout?: ('grid' | 'carousel') | null;
        columns?: ('3' | '4' | '5' | '6') | null;
        /**
         * Number of partners to display
         */
        displayCount?: number | null;
        /**
         * Enable hover animation (subtle scale transform)
         */
        enableHoverAnimation?: boolean | null;
        /**
         * Display logos in grayscale
         */
        grayscale?: boolean | null;
        /**
         * Show color on hover
         */
        colorOnHover?: boolean | null;
        /**
         * Link logos to partner websites
         */
        linkToPartnerWebsite?: boolean | null;
        backgroundColor?: ('light' | 'dark' | 'primary' | 'secondary' | 'transparent') | null;
        padding?: ('none' | 'small' | 'medium' | 'large') | null;
        /**
         * Auto-rotate carousel items
         */
        autoRotate?: boolean | null;
        /**
         * Rotation interval in milliseconds
         */
        rotationInterval?: number | null;
        /**
         * Pause rotation on hover
         */
        pauseOnHover?: boolean | null;
        /**
         * Show navigation dots
         */
        showDots?: boolean | null;
        /**
         * Show navigation arrows
         */
        showArrows?: boolean | null;
        id?: string | null;
        blockName?: string | null;
        blockType: 'partner-logos-block';
      }
    | {
        heading: string;
        subheading?: string | null;
        /**
         * Enable DTF Stickers service tab
         */
        enableDTFStickers?: boolean | null;
        /**
         * Enable Solid T-shirts service tab
         */
        enableSolidTshirts?: boolean | null;
        /**
         * Enable Custom T-shirts service tab
         */
        enableCustomTshirts?: boolean | null;
        /**
         * Default active tab when the block loads
         */
        defaultTab?: ('dtf-stickers' | 'solid-tshirts' | 'custom-tshirts') | null;
        backgroundColor?: ('light' | 'dark' | 'primary' | 'secondary' | 'transparent') | null;
        textColor?: ('light' | 'dark') | null;
        padding?: ('none' | 'small' | 'medium' | 'large') | null;
        id?: string | null;
        blockName?: string | null;
        blockType: 'product-selection-block';
      }
    | {
        heading: string;
        subheading?: string | null;
        description?: string | null;
        calculatorType: 'dtf' | 'custom_tshirts';
        dtfPricingTiers?:
          | {
              /**
               * Minimum length in meters
               */
              minLength: number;
              /**
               * Maximum length in meters (leave empty for unlimited)
               */
              maxLength?: number | null;
              /**
               * Price per unit in INR
               */
              pricePerUnit: number;
              id?: string | null;
            }[]
          | null;
        tshirtPricingFactors?: {
          /**
           * Base price per t-shirt
           */
          basePrice: number;
          quantityDiscounts?:
            | {
                minQuantity: number;
                maxQuantity?: number | null;
                discountPercentage: number;
                id?: string | null;
              }[]
            | null;
          printingMethods?:
            | {
                method: string;
                additionalCost: number;
                id?: string | null;
              }[]
            | null;
          colorOptions?:
            | {
                option: string;
                additionalCost: number;
                id?: string | null;
              }[]
            | null;
          sizeOptions?:
            | {
                size: string;
                additionalCost: number;
                id?: string | null;
              }[]
            | null;
        };
        showRequestQuoteButton?: boolean | null;
        requestQuoteButtonLabel?: string | null;
        requestQuoteButtonLink?: string | null;
        showAddToCartButton?: boolean | null;
        addToCartButtonLabel?: string | null;
        backgroundColor?: ('light' | 'dark' | 'primary' | 'secondary' | 'transparent') | null;
        textColor?: ('light' | 'dark') | null;
        padding?: ('none' | 'small' | 'medium' | 'large') | null;
        id?: string | null;
        blockName?: string | null;
        blockType: 'pricing-calculator-block';
      }
  )[];
  meta?: {
    title?: string | null;
    /**
     * Maximum upload file size: 12MB. Recommended file size for images is <500KB.
     */
    image?: (string | null) | Media;
    description?: string | null;
  };
  publishedAt?: string | null;
  slug?: string | null;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
  _status?: ('draft' | 'published') | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "posts".
 */
export interface Post {
  id: string;
  title: string;
  heroImage?: (string | null) | Media;
  content: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  relatedPosts?: (string | Post)[] | null;
  categories?: (string | Category)[] | null;
  meta?: {
    title?: string | null;
    /**
     * Maximum upload file size: 12MB. Recommended file size for images is <500KB.
     */
    image?: (string | null) | Media;
    description?: string | null;
  };
  publishedAt?: string | null;
  authors?: (string | User)[] | null;
  populatedAuthors?:
    | {
        id?: string | null;
        name?: string | null;
      }[]
    | null;
  slug?: string | null;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
  _status?: ('draft' | 'published') | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media".
 */
export interface Media {
  id: string;
  alt?: string | null;
  caption?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  prefix?: string | null;
  updatedAt: string;
  createdAt: string;
  url?: string | null;
  thumbnailURL?: string | null;
  filename?: string | null;
  mimeType?: string | null;
  filesize?: number | null;
  width?: number | null;
  height?: number | null;
  focalX?: number | null;
  focalY?: number | null;
  sizes?: {
    thumbnail?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    square?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    small?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    medium?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    large?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    xlarge?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    og?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "categories".
 */
export interface Category {
  id: string;
  title: string;
  slug?: string | null;
  slugLock?: boolean | null;
  parent?: (string | null) | Category;
  breadcrumbs?:
    | {
        doc?: (string | null) | Category;
        url?: string | null;
        label?: string | null;
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users".
 */
export interface User {
  id: string;
  name?: string | null;
  /**
   * User role determines access permissions
   */
  role: 'admin' | 'customer';
  /**
   * Link to customer record for customer users
   */
  customerId?: (string | null) | Customer;
  updatedAt: string;
  createdAt: string;
  email: string;
  resetPasswordToken?: string | null;
  resetPasswordExpiration?: string | null;
  salt?: string | null;
  hash?: string | null;
  loginAttempts?: number | null;
  lockUntil?: string | null;
  password?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "customers".
 */
export interface Customer {
  id: string;
  companyName: string;
  email: string;
  firstName: string;
  lastName: string;
  phone: string;
  /**
   * Goods and Services Tax Identification Number
   */
  gstin?: string | null;
  billingAddress: {
    line1: string;
    line2?: string | null;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  shippingAddress?: {
    sameAsBilling?: boolean | null;
    line1?: string | null;
    line2?: string | null;
    city?: string | null;
    state?: string | null;
    postalCode?: string | null;
    country?: string | null;
  };
  customerType: 'retail' | 'wholesale';
  /**
   * Account status - only active accounts can log in
   */
  status: 'active' | 'inactive' | 'suspended';
  /**
   * Credit limit for wholesale customers (in INR)
   */
  creditLimit?: number | null;
  /**
   * Upload business documents (GST certificate, PAN card, etc.)
   */
  documents?:
    | {
        name: string;
        document: string | Media;
        id?: string | null;
      }[]
    | null;
  /**
   * Internal notes about this customer (not visible to customer)
   */
  notes?: string | null;
  /**
   * Initial password for customer portal access (will be used to create user account)
   */
  initialPassword?: string | null;
  /**
   * Linked user account for portal access
   */
  userId?: (string | null) | User;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "CallToActionBlock".
 */
export interface CallToActionBlock {
  richText?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  links?:
    | {
        link: {
          type?: ('reference' | 'custom') | null;
          newTab?: boolean | null;
          reference?:
            | ({
                relationTo: 'pages';
                value: string | Page;
              } | null)
            | ({
                relationTo: 'posts';
                value: string | Post;
              } | null);
          url?: string | null;
          label: string;
          /**
           * Choose how the link should be rendered.
           */
          appearance?: ('default' | 'outline') | null;
        };
        id?: string | null;
      }[]
    | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'cta';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ContentBlock".
 */
export interface ContentBlock {
  columns?:
    | {
        size?: ('oneThird' | 'half' | 'twoThirds' | 'full') | null;
        richText?: {
          root: {
            type: string;
            children: {
              type: string;
              version: number;
              [k: string]: unknown;
            }[];
            direction: ('ltr' | 'rtl') | null;
            format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
            indent: number;
            version: number;
          };
          [k: string]: unknown;
        } | null;
        enableLink?: boolean | null;
        link?: {
          type?: ('reference' | 'custom') | null;
          newTab?: boolean | null;
          reference?:
            | ({
                relationTo: 'pages';
                value: string | Page;
              } | null)
            | ({
                relationTo: 'posts';
                value: string | Post;
              } | null);
          url?: string | null;
          label: string;
          /**
           * Choose how the link should be rendered.
           */
          appearance?: ('default' | 'outline') | null;
        };
        id?: string | null;
      }[]
    | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'content';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "MediaBlock".
 */
export interface MediaBlock {
  media: string | Media;
  id?: string | null;
  blockName?: string | null;
  blockType: 'mediaBlock';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ArchiveBlock".
 */
export interface ArchiveBlock {
  introContent?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  populateBy?: ('collection' | 'selection') | null;
  relationTo?: 'posts' | null;
  categories?: (string | Category)[] | null;
  limit?: number | null;
  selectedDocs?:
    | {
        relationTo: 'posts';
        value: string | Post;
      }[]
    | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'archive';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "FormBlock".
 */
export interface FormBlock {
  form: string | Form;
  enableIntro?: boolean | null;
  introContent?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'formBlock';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "forms".
 */
export interface Form {
  id: string;
  title: string;
  fields?:
    | (
        | {
            name: string;
            label?: string | null;
            width?: number | null;
            required?: boolean | null;
            defaultValue?: boolean | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'checkbox';
          }
        | {
            name: string;
            label?: string | null;
            width?: number | null;
            required?: boolean | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'country';
          }
        | {
            name: string;
            label?: string | null;
            width?: number | null;
            required?: boolean | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'email';
          }
        | {
            message?: {
              root: {
                type: string;
                children: {
                  type: string;
                  version: number;
                  [k: string]: unknown;
                }[];
                direction: ('ltr' | 'rtl') | null;
                format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
                indent: number;
                version: number;
              };
              [k: string]: unknown;
            } | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'message';
          }
        | {
            name: string;
            label?: string | null;
            width?: number | null;
            defaultValue?: number | null;
            required?: boolean | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'number';
          }
        | {
            name: string;
            label?: string | null;
            width?: number | null;
            defaultValue?: string | null;
            placeholder?: string | null;
            options?:
              | {
                  label: string;
                  value: string;
                  id?: string | null;
                }[]
              | null;
            required?: boolean | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'select';
          }
        | {
            name: string;
            label?: string | null;
            width?: number | null;
            required?: boolean | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'state';
          }
        | {
            name: string;
            label?: string | null;
            width?: number | null;
            defaultValue?: string | null;
            required?: boolean | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'text';
          }
        | {
            name: string;
            label?: string | null;
            width?: number | null;
            defaultValue?: string | null;
            required?: boolean | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'textarea';
          }
      )[]
    | null;
  submitButtonLabel?: string | null;
  /**
   * Choose whether to display an on-page message or redirect to a different page after they submit the form.
   */
  confirmationType?: ('message' | 'redirect') | null;
  confirmationMessage?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  redirect?: {
    url: string;
  };
  /**
   * Send custom emails when the form submits. Use comma separated lists to send the same email to multiple recipients. To reference a value from this form, wrap that field's name with double curly brackets, i.e. {{firstName}}. You can use a wildcard {{*}} to output all data and {{*:table}} to format it as an HTML table in the email.
   */
  emails?:
    | {
        emailTo?: string | null;
        cc?: string | null;
        bcc?: string | null;
        replyTo?: string | null;
        emailFrom?: string | null;
        subject: string;
        /**
         * Enter the message that should be sent in this email.
         */
        message?: {
          root: {
            type: string;
            children: {
              type: string;
              version: number;
              [k: string]: unknown;
            }[];
            direction: ('ltr' | 'rtl') | null;
            format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
            indent: number;
            version: number;
          };
          [k: string]: unknown;
        } | null;
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "testimonials".
 */
export interface Testimonial {
  id: string;
  customerName: string;
  companyName: string;
  /**
   * Position/title of the person giving the testimonial
   */
  position?: string | null;
  testimonial: string;
  rating: number;
  /**
   * Company logo (optional)
   */
  companyLogo?: (string | null) | Media;
  /**
   * Customer photo (optional)
   */
  customerPhoto?: (string | null) | Media;
  /**
   * Approve this testimonial for display on the website
   */
  approved?: boolean | null;
  /**
   * Feature this testimonial on the home page
   */
  featured?: boolean | null;
  /**
   * Display order (lower numbers appear first)
   */
  order?: number | null;
  customerType: 'retail' | 'wholesale';
  projectType: 'dtf' | 'custom_tshirts' | 'solid_tshirts' | 'other';
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "recent-work".
 */
export interface RecentWork {
  id: string;
  title: string;
  client: string;
  category: 'dtf' | 'custom_tshirts' | 'corporate_uniforms' | 'event_merchandise' | 'promotional_products' | 'other';
  description: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  images: {
    image: string | Media;
    alt: string;
    caption?: string | null;
    id?: string | null;
  }[];
  /**
   * Feature this work on the home page
   */
  featured?: boolean | null;
  /**
   * When was this project completed?
   */
  completionDate?: string | null;
  /**
   * Related testimonial (if available)
   */
  testimonial?: (string | null) | Testimonial;
  /**
   * Display order (lower numbers appear first)
   */
  order?: number | null;
  slug?: string | null;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "partners".
 */
export interface Partner {
  id: string;
  name: string;
  logo: string | Media;
  /**
   * Partner website URL
   */
  website?: string | null;
  description?: string | null;
  partnerType: 'supplier' | 'distributor' | 'manufacturer' | 'technology' | 'logistics' | 'other';
  /**
   * Is this an active partnership?
   */
  active?: boolean | null;
  /**
   * Feature this partner on the home page
   */
  featured?: boolean | null;
  /**
   * Display order (lower numbers appear first)
   */
  order?: number | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "products".
 */
export interface Product {
  id: string;
  title: string;
  price: number;
  /**
   * Sale price (if applicable)
   */
  salePrice?: number | null;
  /**
   * Select the categories this product belongs to
   */
  categories?: (string | ProductCategory)[] | null;
  images: {
    image: string | Media;
    alt: string;
    id?: string | null;
  }[];
  description: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  sizes?:
    | {
        size: 'XS' | 'S' | 'M' | 'L' | 'XL' | 'XXL' | 'XXXL';
        inventory: number;
        id?: string | null;
      }[]
    | null;
  colors?:
    | {
        name: string;
        /**
         * Hex color code (e.g., #FF0000)
         */
        colorCode: string;
        /**
         * Image showing the product in this color
         */
        image: string | Media;
        id?: string | null;
      }[]
    | null;
  material: 'cotton' | 'polyester' | 'poly-cotton' | 'organic';
  brand?: string | null;
  /**
   * Feature this product on the home page
   */
  featured?: boolean | null;
  paymentTerms: 'full_upfront' | 'split_payment';
  meta?: {
    title?: string | null;
    /**
     * Maximum upload file size: 12MB. Recommended file size for images is <500KB.
     */
    image?: (string | null) | Media;
    description?: string | null;
  };
  publishedAt?: string | null;
  slug?: string | null;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
  _status?: ('draft' | 'published') | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "product-categories".
 */
export interface ProductCategory {
  id: string;
  name: string;
  description?: string | null;
  /**
   * Category image for display purposes
   */
  image?: (string | null) | Media;
  /**
   * Parent category (if this is a subcategory)
   */
  parent?: (string | null) | ProductCategory;
  /**
   * Feature this category on the home page
   */
  featured?: boolean | null;
  slug?: string | null;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "custom-tshirts".
 */
export interface CustomTshirt {
  id: string;
  name: string;
  description?: string | null;
  basePrice: number;
  materials?:
    | {
        material: 'cotton' | 'polyester' | 'poly-cotton' | 'organic-cotton' | 'bamboo' | 'modal';
        priceModifier?: number | null;
        id?: string | null;
      }[]
    | null;
  sizes?:
    | {
        size: 'xs' | 's' | 'm' | 'l' | 'xl' | 'xxl' | 'xxxl';
        priceModifier?: number | null;
        id?: string | null;
      }[]
    | null;
  colors?:
    | {
        colorName: string;
        colorCode?: string | null;
        priceModifier?: number | null;
        id?: string | null;
      }[]
    | null;
  styles?:
    | {
        styleName: 'round-neck' | 'v-neck' | 'polo' | 'hoodie' | 'tank-top' | 'long-sleeve';
        priceModifier?: number | null;
        id?: string | null;
      }[]
    | null;
  images?:
    | {
        image: string | Media;
        alt?: string | null;
        isPrimary?: boolean | null;
        id?: string | null;
      }[]
    | null;
  printPlacements?:
    | {
        placement: 'front' | 'back' | 'left-sleeve' | 'right-sleeve' | 'front-back';
        priceModifier?: number | null;
        id?: string | null;
      }[]
    | null;
  minimumQuantity?: number | null;
  maximumQuantity?: number | null;
  processingTime?: string | null;
  status: 'draft' | 'published' | 'archived';
  featured?: boolean | null;
  tags?:
    | {
        tag: string;
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "solid-tshirts".
 */
export interface SolidTshirt {
  id: string;
  name: string;
  description?: string | null;
  brand: 'maddox' | 'comfort' | 'premium' | 'basic' | 'eco-friendly';
  price: number;
  /**
   * If this product is on sale, enter the original price here
   */
  originalPrice?: number | null;
  materials?:
    | {
        material: 'cotton' | 'polyester' | 'poly-cotton' | 'organic-cotton' | 'bamboo' | 'modal';
        percentage?: number | null;
        id?: string | null;
      }[]
    | null;
  sizes?:
    | {
        size: 'xs' | 's' | 'm' | 'l' | 'xl' | 'xxl' | 'xxxl';
        stock?: number | null;
        id?: string | null;
      }[]
    | null;
  colors?:
    | {
        colorName: string;
        colorCode?: string | null;
        stock?: number | null;
        id?: string | null;
      }[]
    | null;
  styles?:
    | {
        styleName: 'round-neck' | 'v-neck' | 'polo' | 'hoodie' | 'tank-top' | 'long-sleeve';
        stock?: number | null;
        id?: string | null;
      }[]
    | null;
  images?:
    | {
        image: string | Media;
        alt?: string | null;
        isPrimary?: boolean | null;
        /**
         * Which color this image represents
         */
        colorVariant?: string | null;
        id?: string | null;
      }[]
    | null;
  specifications?: {
    weight?: string | null;
    fit?: ('regular' | 'slim' | 'loose' | 'oversized') | null;
    neckType?: ('round' | 'v-neck' | 'collar' | 'hooded') | null;
    sleeveLength?: ('short' | 'long' | 'sleeveless' | 'three-quarter') | null;
  };
  minimumQuantity?: number | null;
  maximumQuantity?: number | null;
  /**
   * Average customer rating (0-5)
   */
  rating?: number | null;
  reviewCount?: number | null;
  status: 'draft' | 'published' | 'out-of-stock' | 'archived';
  featured?: boolean | null;
  onSale?: boolean | null;
  tags?:
    | {
        tag: string;
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "orders".
 */
export interface Order {
  id: string;
  /**
   * Unique order number (auto-generated)
   */
  orderNumber: string;
  customer: string | Customer;
  /**
   * Type of service for this order
   */
  orderType: 'dtf-stickers' | 'solid-tshirts' | 'custom-tshirts';
  /**
   * Service-specific order details
   */
  serviceSpecificData?: {
    dtfStickers?: {
      /**
       * Design file for DTF stickers (.png, .jpg, .svg, .pdf)
       */
      designFile: string | Media;
      stickerDimensions: {
        /**
         * Width in centimeters
         */
        width: number;
        /**
         * Height in centimeters
         */
        height: number;
        isCustomSize?: boolean | null;
      };
      material: 'matte' | 'glossy' | 'transparent' | 'holographic';
      finish: 'standard' | 'laminated' | 'uv-resistant';
      /**
       * Quantity in meters
       */
      quantity: number;
      /**
       * Price per meter based on quantity tier
       */
      pricePerUnit: number;
    };
    solidTshirts?:
      | {
          productVariant: string | Product;
          size: 'xs' | 's' | 'm' | 'l' | 'xl' | 'xxl' | 'xxxl';
          color: string;
          material: 'cotton' | 'polyester' | 'poly-cotton' | 'organic';
          /**
           * T-shirt brand/manufacturer
           */
          brand?: string | null;
          quantity: number;
          unitPrice: number;
          totalPrice: number;
          id?: string | null;
        }[]
      | null;
    customTshirts?: {
      tshirtSelection: {
        /**
         * Base t-shirt product
         */
        baseProduct: string | Product;
        size: 'xs' | 's' | 'm' | 'l' | 'xl' | 'xxl' | 'xxxl';
        color: string;
        material: 'cotton' | 'polyester' | 'poly-cotton' | 'organic';
        style?: ('crew-neck' | 'v-neck' | 'polo' | 'tank-top') | null;
      };
      /**
       * Customer design file
       */
      designFile: string | Media;
      /**
       * Generated mockup image
       */
      mockupImage?: (string | null) | Media;
      printPlacement: 'front' | 'back' | 'left-sleeve' | 'right-sleeve' | 'front-back';
      printSize: {
        /**
         * Print width in centimeters
         */
        width: number;
        /**
         * Print height in centimeters
         */
        height: number;
      };
      quantity: number;
      /**
       * 50% advance payment amount
       */
      advanceAmount: number;
      /**
       * Remaining 50% payment amount
       */
      remainingAmount: number;
    };
  };
  subtotal: number;
  tax: number;
  shipping: number;
  discount?: number | null;
  total: number;
  status: 'pending' | 'processing' | 'partially_paid' | 'paid' | 'shipped' | 'delivered' | 'cancelled' | 'refunded';
  paymentMethod?: ('razorpay' | 'bank_transfer' | 'cod') | null;
  paymentStatus: 'pending' | 'partially_paid' | 'paid' | 'failed' | 'refunded';
  /**
   * DTF Stickers & Solid T-shirts: 100% upfront, Custom T-shirts: 50/50 split
   */
  paymentTerms: 'full_upfront' | 'split_payment';
  transactions?:
    | {
        transactionId: string;
        amount: number;
        method: 'razorpay' | 'bank_transfer' | 'cod';
        status: 'success' | 'failed' | 'pending';
        date: string;
        id?: string | null;
      }[]
    | null;
  shippingDetails: {
    address: {
      line1: string;
      line2?: string | null;
      city: string;
      state: string;
      postalCode: string;
      country: string;
    };
    trackingNumber?: string | null;
    carrier?: string | null;
    estimatedDelivery?: string | null;
    shiprocketOrderId?: string | null;
  };
  /**
   * Internal notes about this order (not visible to customer)
   */
  notes?: string | null;
  /**
   * Notes from the customer
   */
  customerNotes?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "marketing-expenses".
 */
export interface MarketingExpense {
  id: string;
  campaign: string;
  platform:
    | 'facebook'
    | 'instagram'
    | 'google'
    | 'linkedin'
    | 'twitter'
    | 'youtube'
    | 'tiktok'
    | 'print'
    | 'outdoor'
    | 'radio'
    | 'television'
    | 'trade_show'
    | 'other';
  amount: number;
  date: string;
  /**
   * End date for campaigns that run over a period
   */
  endDate?: string | null;
  description?: string | null;
  targetAudience: 'retail' | 'wholesale' | 'both';
  metrics?: {
    impressions?: number | null;
    clicks?: number | null;
    conversions?: number | null;
    revenue?: number | null;
    roi?: number | null;
  };
  attachments?:
    | {
        name: string;
        file: string | Media;
        id?: string | null;
      }[]
    | null;
  status: 'planned' | 'active' | 'completed' | 'cancelled';
  /**
   * Internal notes about this marketing expense
   */
  notes?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "redirects".
 */
export interface Redirect {
  id: string;
  /**
   * You will need to rebuild the website when changing this field.
   */
  from: string;
  to?: {
    type?: ('reference' | 'custom') | null;
    reference?:
      | ({
          relationTo: 'pages';
          value: string | Page;
        } | null)
      | ({
          relationTo: 'posts';
          value: string | Post;
        } | null);
    url?: string | null;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "form-submissions".
 */
export interface FormSubmission {
  id: string;
  form: string | Form;
  submissionData?:
    | {
        field: string;
        value: string;
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This is a collection of automatically created search results. These results are used by the global site search and will be updated automatically as documents in the CMS are created or updated.
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "search".
 */
export interface Search {
  id: string;
  title?: string | null;
  priority?: number | null;
  doc: {
    relationTo: 'posts';
    value: string | Post;
  };
  slug?: string | null;
  meta?: {
    title?: string | null;
    description?: string | null;
    image?: (string | null) | Media;
  };
  categories?:
    | {
        relationTo?: string | null;
        id?: string | null;
        title?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-jobs".
 */
export interface PayloadJob {
  id: string;
  /**
   * Input data provided to the job
   */
  input?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  taskStatus?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  completedAt?: string | null;
  totalTried?: number | null;
  /**
   * If hasError is true this job will not be retried
   */
  hasError?: boolean | null;
  /**
   * If hasError is true, this is the error that caused it
   */
  error?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  /**
   * Task execution log
   */
  log?:
    | {
        executedAt: string;
        completedAt: string;
        taskSlug: 'inline' | 'schedulePublish';
        taskID: string;
        input?:
          | {
              [k: string]: unknown;
            }
          | unknown[]
          | string
          | number
          | boolean
          | null;
        output?:
          | {
              [k: string]: unknown;
            }
          | unknown[]
          | string
          | number
          | boolean
          | null;
        state: 'failed' | 'succeeded';
        error?:
          | {
              [k: string]: unknown;
            }
          | unknown[]
          | string
          | number
          | boolean
          | null;
        id?: string | null;
      }[]
    | null;
  taskSlug?: ('inline' | 'schedulePublish') | null;
  queue?: string | null;
  waitUntil?: string | null;
  processing?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents".
 */
export interface PayloadLockedDocument {
  id: string;
  document?:
    | ({
        relationTo: 'pages';
        value: string | Page;
      } | null)
    | ({
        relationTo: 'posts';
        value: string | Post;
      } | null)
    | ({
        relationTo: 'media';
        value: string | Media;
      } | null)
    | ({
        relationTo: 'categories';
        value: string | Category;
      } | null)
    | ({
        relationTo: 'users';
        value: string | User;
      } | null)
    | ({
        relationTo: 'products';
        value: string | Product;
      } | null)
    | ({
        relationTo: 'product-categories';
        value: string | ProductCategory;
      } | null)
    | ({
        relationTo: 'custom-tshirts';
        value: string | CustomTshirt;
      } | null)
    | ({
        relationTo: 'solid-tshirts';
        value: string | SolidTshirt;
      } | null)
    | ({
        relationTo: 'customers';
        value: string | Customer;
      } | null)
    | ({
        relationTo: 'orders';
        value: string | Order;
      } | null)
    | ({
        relationTo: 'marketing-expenses';
        value: string | MarketingExpense;
      } | null)
    | ({
        relationTo: 'testimonials';
        value: string | Testimonial;
      } | null)
    | ({
        relationTo: 'partners';
        value: string | Partner;
      } | null)
    | ({
        relationTo: 'recent-work';
        value: string | RecentWork;
      } | null)
    | ({
        relationTo: 'redirects';
        value: string | Redirect;
      } | null)
    | ({
        relationTo: 'forms';
        value: string | Form;
      } | null)
    | ({
        relationTo: 'form-submissions';
        value: string | FormSubmission;
      } | null)
    | ({
        relationTo: 'search';
        value: string | Search;
      } | null)
    | ({
        relationTo: 'payload-jobs';
        value: string | PayloadJob;
      } | null);
  globalSlug?: string | null;
  user: {
    relationTo: 'users';
    value: string | User;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences".
 */
export interface PayloadPreference {
  id: string;
  user: {
    relationTo: 'users';
    value: string | User;
  };
  key?: string | null;
  value?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations".
 */
export interface PayloadMigration {
  id: string;
  name?: string | null;
  batch?: number | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "pages_select".
 */
export interface PagesSelect<T extends boolean = true> {
  title?: T;
  hero?:
    | T
    | {
        type?: T;
        richText?: T;
        links?:
          | T
          | {
              link?:
                | T
                | {
                    type?: T;
                    newTab?: T;
                    reference?: T;
                    url?: T;
                    label?: T;
                    appearance?: T;
                  };
              id?: T;
            };
        media?: T;
      };
  layout?:
    | T
    | {
        cta?: T | CallToActionBlockSelect<T>;
        content?: T | ContentBlockSelect<T>;
        mediaBlock?: T | MediaBlockSelect<T>;
        archive?: T | ArchiveBlockSelect<T>;
        formBlock?: T | FormBlockSelect<T>;
        'hero-banner'?:
          | T
          | {
              title?: T;
              subtitle?: T;
              content?: T;
              backgroundImage?: T;
              backgroundOverlay?: T;
              overlayOpacity?: T;
              textColor?: T;
              textAlignment?: T;
              height?: T;
              primaryCTA?:
                | T
                | {
                    label?: T;
                    link?: T;
                    style?: T;
                    size?: T;
                  };
              secondaryCTA?:
                | T
                | {
                    label?: T;
                    link?: T;
                    style?: T;
                    size?: T;
                  };
              id?: T;
              blockName?: T;
            };
        'featured-services'?:
          | T
          | {
              heading?: T;
              subheading?: T;
              services?:
                | T
                | {
                    title?: T;
                    description?: T;
                    image?: T;
                    link?: T;
                    linkLabel?: T;
                    id?: T;
                  };
              autoRotate?: T;
              rotationInterval?: T;
              pauseOnHover?: T;
              showDots?: T;
              showArrows?: T;
              backgroundColor?: T;
              textColor?: T;
              padding?: T;
              id?: T;
              blockName?: T;
            };
        'testimonials-block'?:
          | T
          | {
              heading?: T;
              subheading?: T;
              testimonials?: T;
              layout?: T;
              displayCount?: T;
              showRating?: T;
              showCompanyLogo?: T;
              showCustomerPhoto?: T;
              backgroundColor?: T;
              textColor?: T;
              padding?: T;
              autoRotate?: T;
              rotationInterval?: T;
              pauseOnHover?: T;
              showDots?: T;
              showArrows?: T;
              id?: T;
              blockName?: T;
            };
        'recent-work-block'?:
          | T
          | {
              heading?: T;
              subheading?: T;
              projects?: T;
              layout?: T;
              displayCount?: T;
              enableFiltering?: T;
              categories?: T;
              showTitle?: T;
              showClient?: T;
              showCategory?: T;
              enableLightbox?: T;
              linkToDetailPage?: T;
              backgroundColor?: T;
              textColor?: T;
              padding?: T;
              viewAllButton?:
                | T
                | {
                    show?: T;
                    label?: T;
                    link?: T;
                  };
              id?: T;
              blockName?: T;
            };
        'partner-logos-block'?:
          | T
          | {
              heading?: T;
              subheading?: T;
              partners?: T;
              layout?: T;
              columns?: T;
              displayCount?: T;
              enableHoverAnimation?: T;
              grayscale?: T;
              colorOnHover?: T;
              linkToPartnerWebsite?: T;
              backgroundColor?: T;
              padding?: T;
              autoRotate?: T;
              rotationInterval?: T;
              pauseOnHover?: T;
              showDots?: T;
              showArrows?: T;
              id?: T;
              blockName?: T;
            };
        'product-selection-block'?:
          | T
          | {
              heading?: T;
              subheading?: T;
              enableDTFStickers?: T;
              enableSolidTshirts?: T;
              enableCustomTshirts?: T;
              defaultTab?: T;
              backgroundColor?: T;
              textColor?: T;
              padding?: T;
              id?: T;
              blockName?: T;
            };
        'pricing-calculator-block'?:
          | T
          | {
              heading?: T;
              subheading?: T;
              description?: T;
              calculatorType?: T;
              dtfPricingTiers?:
                | T
                | {
                    minLength?: T;
                    maxLength?: T;
                    pricePerUnit?: T;
                    id?: T;
                  };
              tshirtPricingFactors?:
                | T
                | {
                    basePrice?: T;
                    quantityDiscounts?:
                      | T
                      | {
                          minQuantity?: T;
                          maxQuantity?: T;
                          discountPercentage?: T;
                          id?: T;
                        };
                    printingMethods?:
                      | T
                      | {
                          method?: T;
                          additionalCost?: T;
                          id?: T;
                        };
                    colorOptions?:
                      | T
                      | {
                          option?: T;
                          additionalCost?: T;
                          id?: T;
                        };
                    sizeOptions?:
                      | T
                      | {
                          size?: T;
                          additionalCost?: T;
                          id?: T;
                        };
                  };
              showRequestQuoteButton?: T;
              requestQuoteButtonLabel?: T;
              requestQuoteButtonLink?: T;
              showAddToCartButton?: T;
              addToCartButtonLabel?: T;
              backgroundColor?: T;
              textColor?: T;
              padding?: T;
              id?: T;
              blockName?: T;
            };
      };
  meta?:
    | T
    | {
        title?: T;
        image?: T;
        description?: T;
      };
  publishedAt?: T;
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
  _status?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "CallToActionBlock_select".
 */
export interface CallToActionBlockSelect<T extends boolean = true> {
  richText?: T;
  links?:
    | T
    | {
        link?:
          | T
          | {
              type?: T;
              newTab?: T;
              reference?: T;
              url?: T;
              label?: T;
              appearance?: T;
            };
        id?: T;
      };
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ContentBlock_select".
 */
export interface ContentBlockSelect<T extends boolean = true> {
  columns?:
    | T
    | {
        size?: T;
        richText?: T;
        enableLink?: T;
        link?:
          | T
          | {
              type?: T;
              newTab?: T;
              reference?: T;
              url?: T;
              label?: T;
              appearance?: T;
            };
        id?: T;
      };
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "MediaBlock_select".
 */
export interface MediaBlockSelect<T extends boolean = true> {
  media?: T;
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ArchiveBlock_select".
 */
export interface ArchiveBlockSelect<T extends boolean = true> {
  introContent?: T;
  populateBy?: T;
  relationTo?: T;
  categories?: T;
  limit?: T;
  selectedDocs?: T;
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "FormBlock_select".
 */
export interface FormBlockSelect<T extends boolean = true> {
  form?: T;
  enableIntro?: T;
  introContent?: T;
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "posts_select".
 */
export interface PostsSelect<T extends boolean = true> {
  title?: T;
  heroImage?: T;
  content?: T;
  relatedPosts?: T;
  categories?: T;
  meta?:
    | T
    | {
        title?: T;
        image?: T;
        description?: T;
      };
  publishedAt?: T;
  authors?: T;
  populatedAuthors?:
    | T
    | {
        id?: T;
        name?: T;
      };
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
  _status?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media_select".
 */
export interface MediaSelect<T extends boolean = true> {
  alt?: T;
  caption?: T;
  prefix?: T;
  updatedAt?: T;
  createdAt?: T;
  url?: T;
  thumbnailURL?: T;
  filename?: T;
  mimeType?: T;
  filesize?: T;
  width?: T;
  height?: T;
  focalX?: T;
  focalY?: T;
  sizes?:
    | T
    | {
        thumbnail?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        square?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        small?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        medium?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        large?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        xlarge?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        og?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
      };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "categories_select".
 */
export interface CategoriesSelect<T extends boolean = true> {
  title?: T;
  slug?: T;
  slugLock?: T;
  parent?: T;
  breadcrumbs?:
    | T
    | {
        doc?: T;
        url?: T;
        label?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users_select".
 */
export interface UsersSelect<T extends boolean = true> {
  name?: T;
  role?: T;
  customerId?: T;
  updatedAt?: T;
  createdAt?: T;
  email?: T;
  resetPasswordToken?: T;
  resetPasswordExpiration?: T;
  salt?: T;
  hash?: T;
  loginAttempts?: T;
  lockUntil?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "products_select".
 */
export interface ProductsSelect<T extends boolean = true> {
  title?: T;
  price?: T;
  salePrice?: T;
  categories?: T;
  images?:
    | T
    | {
        image?: T;
        alt?: T;
        id?: T;
      };
  description?: T;
  sizes?:
    | T
    | {
        size?: T;
        inventory?: T;
        id?: T;
      };
  colors?:
    | T
    | {
        name?: T;
        colorCode?: T;
        image?: T;
        id?: T;
      };
  material?: T;
  brand?: T;
  featured?: T;
  paymentTerms?: T;
  meta?:
    | T
    | {
        title?: T;
        image?: T;
        description?: T;
      };
  publishedAt?: T;
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
  _status?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "product-categories_select".
 */
export interface ProductCategoriesSelect<T extends boolean = true> {
  name?: T;
  description?: T;
  image?: T;
  parent?: T;
  featured?: T;
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "custom-tshirts_select".
 */
export interface CustomTshirtsSelect<T extends boolean = true> {
  name?: T;
  description?: T;
  basePrice?: T;
  materials?:
    | T
    | {
        material?: T;
        priceModifier?: T;
        id?: T;
      };
  sizes?:
    | T
    | {
        size?: T;
        priceModifier?: T;
        id?: T;
      };
  colors?:
    | T
    | {
        colorName?: T;
        colorCode?: T;
        priceModifier?: T;
        id?: T;
      };
  styles?:
    | T
    | {
        styleName?: T;
        priceModifier?: T;
        id?: T;
      };
  images?:
    | T
    | {
        image?: T;
        alt?: T;
        isPrimary?: T;
        id?: T;
      };
  printPlacements?:
    | T
    | {
        placement?: T;
        priceModifier?: T;
        id?: T;
      };
  minimumQuantity?: T;
  maximumQuantity?: T;
  processingTime?: T;
  status?: T;
  featured?: T;
  tags?:
    | T
    | {
        tag?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "solid-tshirts_select".
 */
export interface SolidTshirtsSelect<T extends boolean = true> {
  name?: T;
  description?: T;
  brand?: T;
  price?: T;
  originalPrice?: T;
  materials?:
    | T
    | {
        material?: T;
        percentage?: T;
        id?: T;
      };
  sizes?:
    | T
    | {
        size?: T;
        stock?: T;
        id?: T;
      };
  colors?:
    | T
    | {
        colorName?: T;
        colorCode?: T;
        stock?: T;
        id?: T;
      };
  styles?:
    | T
    | {
        styleName?: T;
        stock?: T;
        id?: T;
      };
  images?:
    | T
    | {
        image?: T;
        alt?: T;
        isPrimary?: T;
        colorVariant?: T;
        id?: T;
      };
  specifications?:
    | T
    | {
        weight?: T;
        fit?: T;
        neckType?: T;
        sleeveLength?: T;
      };
  minimumQuantity?: T;
  maximumQuantity?: T;
  rating?: T;
  reviewCount?: T;
  status?: T;
  featured?: T;
  onSale?: T;
  tags?:
    | T
    | {
        tag?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "customers_select".
 */
export interface CustomersSelect<T extends boolean = true> {
  companyName?: T;
  email?: T;
  firstName?: T;
  lastName?: T;
  phone?: T;
  gstin?: T;
  billingAddress?:
    | T
    | {
        line1?: T;
        line2?: T;
        city?: T;
        state?: T;
        postalCode?: T;
        country?: T;
      };
  shippingAddress?:
    | T
    | {
        sameAsBilling?: T;
        line1?: T;
        line2?: T;
        city?: T;
        state?: T;
        postalCode?: T;
        country?: T;
      };
  customerType?: T;
  status?: T;
  creditLimit?: T;
  documents?:
    | T
    | {
        name?: T;
        document?: T;
        id?: T;
      };
  notes?: T;
  initialPassword?: T;
  userId?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "orders_select".
 */
export interface OrdersSelect<T extends boolean = true> {
  orderNumber?: T;
  customer?: T;
  orderType?: T;
  serviceSpecificData?:
    | T
    | {
        dtfStickers?:
          | T
          | {
              designFile?: T;
              stickerDimensions?:
                | T
                | {
                    width?: T;
                    height?: T;
                    isCustomSize?: T;
                  };
              material?: T;
              finish?: T;
              quantity?: T;
              pricePerUnit?: T;
            };
        solidTshirts?:
          | T
          | {
              productVariant?: T;
              size?: T;
              color?: T;
              material?: T;
              brand?: T;
              quantity?: T;
              unitPrice?: T;
              totalPrice?: T;
              id?: T;
            };
        customTshirts?:
          | T
          | {
              tshirtSelection?:
                | T
                | {
                    baseProduct?: T;
                    size?: T;
                    color?: T;
                    material?: T;
                    style?: T;
                  };
              designFile?: T;
              mockupImage?: T;
              printPlacement?: T;
              printSize?:
                | T
                | {
                    width?: T;
                    height?: T;
                  };
              quantity?: T;
              advanceAmount?: T;
              remainingAmount?: T;
            };
      };
  subtotal?: T;
  tax?: T;
  shipping?: T;
  discount?: T;
  total?: T;
  status?: T;
  paymentMethod?: T;
  paymentStatus?: T;
  paymentTerms?: T;
  transactions?:
    | T
    | {
        transactionId?: T;
        amount?: T;
        method?: T;
        status?: T;
        date?: T;
        id?: T;
      };
  shippingDetails?:
    | T
    | {
        address?:
          | T
          | {
              line1?: T;
              line2?: T;
              city?: T;
              state?: T;
              postalCode?: T;
              country?: T;
            };
        trackingNumber?: T;
        carrier?: T;
        estimatedDelivery?: T;
        shiprocketOrderId?: T;
      };
  notes?: T;
  customerNotes?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "marketing-expenses_select".
 */
export interface MarketingExpensesSelect<T extends boolean = true> {
  campaign?: T;
  platform?: T;
  amount?: T;
  date?: T;
  endDate?: T;
  description?: T;
  targetAudience?: T;
  metrics?:
    | T
    | {
        impressions?: T;
        clicks?: T;
        conversions?: T;
        revenue?: T;
        roi?: T;
      };
  attachments?:
    | T
    | {
        name?: T;
        file?: T;
        id?: T;
      };
  status?: T;
  notes?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "testimonials_select".
 */
export interface TestimonialsSelect<T extends boolean = true> {
  customerName?: T;
  companyName?: T;
  position?: T;
  testimonial?: T;
  rating?: T;
  companyLogo?: T;
  customerPhoto?: T;
  approved?: T;
  featured?: T;
  order?: T;
  customerType?: T;
  projectType?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "partners_select".
 */
export interface PartnersSelect<T extends boolean = true> {
  name?: T;
  logo?: T;
  website?: T;
  description?: T;
  partnerType?: T;
  active?: T;
  featured?: T;
  order?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "recent-work_select".
 */
export interface RecentWorkSelect<T extends boolean = true> {
  title?: T;
  client?: T;
  category?: T;
  description?: T;
  images?:
    | T
    | {
        image?: T;
        alt?: T;
        caption?: T;
        id?: T;
      };
  featured?: T;
  completionDate?: T;
  testimonial?: T;
  order?: T;
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "redirects_select".
 */
export interface RedirectsSelect<T extends boolean = true> {
  from?: T;
  to?:
    | T
    | {
        type?: T;
        reference?: T;
        url?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "forms_select".
 */
export interface FormsSelect<T extends boolean = true> {
  title?: T;
  fields?:
    | T
    | {
        checkbox?:
          | T
          | {
              name?: T;
              label?: T;
              width?: T;
              required?: T;
              defaultValue?: T;
              id?: T;
              blockName?: T;
            };
        country?:
          | T
          | {
              name?: T;
              label?: T;
              width?: T;
              required?: T;
              id?: T;
              blockName?: T;
            };
        email?:
          | T
          | {
              name?: T;
              label?: T;
              width?: T;
              required?: T;
              id?: T;
              blockName?: T;
            };
        message?:
          | T
          | {
              message?: T;
              id?: T;
              blockName?: T;
            };
        number?:
          | T
          | {
              name?: T;
              label?: T;
              width?: T;
              defaultValue?: T;
              required?: T;
              id?: T;
              blockName?: T;
            };
        select?:
          | T
          | {
              name?: T;
              label?: T;
              width?: T;
              defaultValue?: T;
              placeholder?: T;
              options?:
                | T
                | {
                    label?: T;
                    value?: T;
                    id?: T;
                  };
              required?: T;
              id?: T;
              blockName?: T;
            };
        state?:
          | T
          | {
              name?: T;
              label?: T;
              width?: T;
              required?: T;
              id?: T;
              blockName?: T;
            };
        text?:
          | T
          | {
              name?: T;
              label?: T;
              width?: T;
              defaultValue?: T;
              required?: T;
              id?: T;
              blockName?: T;
            };
        textarea?:
          | T
          | {
              name?: T;
              label?: T;
              width?: T;
              defaultValue?: T;
              required?: T;
              id?: T;
              blockName?: T;
            };
      };
  submitButtonLabel?: T;
  confirmationType?: T;
  confirmationMessage?: T;
  redirect?:
    | T
    | {
        url?: T;
      };
  emails?:
    | T
    | {
        emailTo?: T;
        cc?: T;
        bcc?: T;
        replyTo?: T;
        emailFrom?: T;
        subject?: T;
        message?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "form-submissions_select".
 */
export interface FormSubmissionsSelect<T extends boolean = true> {
  form?: T;
  submissionData?:
    | T
    | {
        field?: T;
        value?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "search_select".
 */
export interface SearchSelect<T extends boolean = true> {
  title?: T;
  priority?: T;
  doc?: T;
  slug?: T;
  meta?:
    | T
    | {
        title?: T;
        description?: T;
        image?: T;
      };
  categories?:
    | T
    | {
        relationTo?: T;
        id?: T;
        title?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-jobs_select".
 */
export interface PayloadJobsSelect<T extends boolean = true> {
  input?: T;
  taskStatus?: T;
  completedAt?: T;
  totalTried?: T;
  hasError?: T;
  error?: T;
  log?:
    | T
    | {
        executedAt?: T;
        completedAt?: T;
        taskSlug?: T;
        taskID?: T;
        input?: T;
        output?: T;
        state?: T;
        error?: T;
        id?: T;
      };
  taskSlug?: T;
  queue?: T;
  waitUntil?: T;
  processing?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents_select".
 */
export interface PayloadLockedDocumentsSelect<T extends boolean = true> {
  document?: T;
  globalSlug?: T;
  user?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences_select".
 */
export interface PayloadPreferencesSelect<T extends boolean = true> {
  user?: T;
  key?: T;
  value?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations_select".
 */
export interface PayloadMigrationsSelect<T extends boolean = true> {
  name?: T;
  batch?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "header".
 */
export interface Header {
  id: string;
  navItems?:
    | {
        link: {
          type?: ('reference' | 'custom') | null;
          newTab?: boolean | null;
          reference?:
            | ({
                relationTo: 'pages';
                value: string | Page;
              } | null)
            | ({
                relationTo: 'posts';
                value: string | Post;
              } | null);
          url?: string | null;
          label: string;
        };
        id?: string | null;
      }[]
    | null;
  updatedAt?: string | null;
  createdAt?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "footer".
 */
export interface Footer {
  id: string;
  navItems?:
    | {
        link: {
          type?: ('reference' | 'custom') | null;
          newTab?: boolean | null;
          reference?:
            | ({
                relationTo: 'pages';
                value: string | Page;
              } | null)
            | ({
                relationTo: 'posts';
                value: string | Post;
              } | null);
          url?: string | null;
          label: string;
        };
        id?: string | null;
      }[]
    | null;
  updatedAt?: string | null;
  createdAt?: string | null;
}
/**
 * Configure DTF printing pricing tiers, discounts, and payment terms
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "dtf-pricing".
 */
export interface DtfPricing {
  id: string;
  /**
   * Enable/disable DTF pricing system globally
   */
  isActive: boolean;
  /**
   * Last time pricing was updated
   */
  lastUpdated?: string | null;
  /**
   * Admin who last updated pricing
   */
  updatedBy?: (string | null) | User;
  /**
   * Pricing configuration version
   */
  version?: string | null;
  /**
   * Simplified length-based pricing model
   */
  pricingModel?: string | null;
  /**
   * Fixed measurement unit: meters
   */
  measurementUnit?: string | null;
  currency: 'INR' | 'USD' | 'EUR';
  basePricing?: {
    /**
     * Minimum order value required (in selected currency)
     */
    minimumOrderValue?: number | null;
    /**
     * One-time setup fee per order (in selected currency)
     */
    setupFee?: number | null;
    /**
     * Multiplier for rush orders (e.g., 1.5 = 50% extra)
     */
    rushOrderMultiplier?: number | null;
  };
  /**
   * Configure pricing based on length ranges (in meters)
   */
  sizeTiers?:
    | {
        /**
         * e.g., "0-5 meters", "5-10 meters", "10+ meters"
         */
        tierName: string;
        lengthRange: {
          /**
           * Minimum length in meters
           */
          minLength: number;
          /**
           * Maximum length in meters (leave empty for unlimited)
           */
          maxLength?: number | null;
        };
        pricing: {
          /**
           * Price per meter in INR
           */
          pricePerMeter: number;
        };
        /**
         * Enable/disable this pricing tier
         */
        isActive?: boolean | null;
        id?: string | null;
      }[]
    | null;
  /**
   * Configure bulk order discounts
   */
  quantityDiscounts?:
    | {
        minQuantity: number;
        /**
         * Leave empty for unlimited
         */
        maxQuantity?: number | null;
        discountType: 'percentage' | 'fixed_amount' | 'fixed_price';
        /**
         * Discount percentage (0-100) or fixed amount
         */
        discountValue: number;
        isActive?: boolean | null;
        id?: string | null;
      }[]
    | null;
  /**
   * Different material types with pricing adjustments
   */
  materialOptions?:
    | {
        /**
         * e.g., "Standard Vinyl", "Premium Vinyl", "Eco-Friendly"
         */
        materialName: string;
        /**
         * Description of material properties
         */
        description?: string | null;
        priceAdjustment: {
          adjustmentType: 'percentage_increase' | 'fixed_increase' | 'multiplier';
          /**
           * Adjustment value (percentage, amount, or multiplier)
           */
          adjustmentValue: number;
        };
        /**
         * Mark as default material option
         */
        isDefault?: boolean | null;
        isActive?: boolean | null;
        id?: string | null;
      }[]
    | null;
  /**
   * Extra services like lamination, cutting, etc.
   */
  additionalServices?:
    | {
        serviceName: string;
        description?: string | null;
        pricingType: 'per_item' | 'per_order' | 'percentage';
        price: number;
        isActive?: boolean | null;
        id?: string | null;
      }[]
    | null;
  paymentTerms: {
    defaultTerms: 'full_upfront' | 'split_payment' | 'custom_split';
    /**
     * Minimum advance payment percentage required
     */
    minimumAdvancePercentage?: number | null;
  };
  updatedAt?: string | null;
  createdAt?: string | null;
}
/**
 * Manage site-wide settings including logos, branding, and general configuration
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "site-settings".
 */
export interface SiteSetting {
  id: string;
  /**
   * The name of your site/brand
   */
  siteName: string;
  /**
   * Brief description of your site/business
   */
  siteDescription?: string | null;
  branding?: {
    /**
     * Main site logo (recommended: SVG or PNG, max 300px width)
     */
    logo?: (string | null) | Media;
    /**
     * Alt text for the logo (for accessibility)
     */
    logoAlt?: string | null;
    /**
     * Logo width in pixels (default: 193px)
     */
    logoWidth?: number | null;
    /**
     * Logo height in pixels (default: 34px)
     */
    logoHeight?: number | null;
    /**
     * Site favicon (recommended: ICO or PNG, 32x32px)
     */
    favicon?: (string | null) | Media;
  };
  contact?: {
    /**
     * Primary contact email
     */
    email?: string | null;
    /**
     * Primary contact phone number
     */
    phone?: string | null;
    /**
     * Business address
     */
    address?: string | null;
  };
  social?: {
    /**
     * Facebook page URL
     */
    facebook?: string | null;
    /**
     * Instagram profile URL
     */
    instagram?: string | null;
    /**
     * Twitter/X profile URL
     */
    twitter?: string | null;
    /**
     * LinkedIn company page URL
     */
    linkedin?: string | null;
  };
  seo?: {
    /**
     * Default meta title for pages without specific SEO settings
     */
    metaTitle?: string | null;
    /**
     * Default meta description for pages without specific SEO settings
     */
    metaDescription?: string | null;
    /**
     * Default Open Graph image (recommended: 1200x630px)
     */
    ogImage?: (string | null) | Media;
  };
  /**
   * Last time settings were updated
   */
  lastUpdated?: string | null;
  /**
   * Admin who last updated settings
   */
  updatedBy?: (string | null) | User;
  updatedAt?: string | null;
  createdAt?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "header_select".
 */
export interface HeaderSelect<T extends boolean = true> {
  navItems?:
    | T
    | {
        link?:
          | T
          | {
              type?: T;
              newTab?: T;
              reference?: T;
              url?: T;
              label?: T;
            };
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
  globalType?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "footer_select".
 */
export interface FooterSelect<T extends boolean = true> {
  navItems?:
    | T
    | {
        link?:
          | T
          | {
              type?: T;
              newTab?: T;
              reference?: T;
              url?: T;
              label?: T;
            };
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
  globalType?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "dtf-pricing_select".
 */
export interface DtfPricingSelect<T extends boolean = true> {
  isActive?: T;
  lastUpdated?: T;
  updatedBy?: T;
  version?: T;
  pricingModel?: T;
  measurementUnit?: T;
  currency?: T;
  basePricing?:
    | T
    | {
        minimumOrderValue?: T;
        setupFee?: T;
        rushOrderMultiplier?: T;
      };
  sizeTiers?:
    | T
    | {
        tierName?: T;
        lengthRange?:
          | T
          | {
              minLength?: T;
              maxLength?: T;
            };
        pricing?:
          | T
          | {
              pricePerMeter?: T;
            };
        isActive?: T;
        id?: T;
      };
  quantityDiscounts?:
    | T
    | {
        minQuantity?: T;
        maxQuantity?: T;
        discountType?: T;
        discountValue?: T;
        isActive?: T;
        id?: T;
      };
  materialOptions?:
    | T
    | {
        materialName?: T;
        description?: T;
        priceAdjustment?:
          | T
          | {
              adjustmentType?: T;
              adjustmentValue?: T;
            };
        isDefault?: T;
        isActive?: T;
        id?: T;
      };
  additionalServices?:
    | T
    | {
        serviceName?: T;
        description?: T;
        pricingType?: T;
        price?: T;
        isActive?: T;
        id?: T;
      };
  paymentTerms?:
    | T
    | {
        defaultTerms?: T;
        minimumAdvancePercentage?: T;
      };
  updatedAt?: T;
  createdAt?: T;
  globalType?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "site-settings_select".
 */
export interface SiteSettingsSelect<T extends boolean = true> {
  siteName?: T;
  siteDescription?: T;
  branding?:
    | T
    | {
        logo?: T;
        logoAlt?: T;
        logoWidth?: T;
        logoHeight?: T;
        favicon?: T;
      };
  contact?:
    | T
    | {
        email?: T;
        phone?: T;
        address?: T;
      };
  social?:
    | T
    | {
        facebook?: T;
        instagram?: T;
        twitter?: T;
        linkedin?: T;
      };
  seo?:
    | T
    | {
        metaTitle?: T;
        metaDescription?: T;
        ogImage?: T;
      };
  lastUpdated?: T;
  updatedBy?: T;
  updatedAt?: T;
  createdAt?: T;
  globalType?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskSchedulePublish".
 */
export interface TaskSchedulePublish {
  input: {
    type?: ('publish' | 'unpublish') | null;
    locale?: string | null;
    doc?:
      | ({
          relationTo: 'pages';
          value: string | Page;
        } | null)
      | ({
          relationTo: 'posts';
          value: string | Post;
        } | null)
      | ({
          relationTo: 'products';
          value: string | Product;
        } | null);
    global?: string | null;
    user?: (string | null) | User;
  };
  output?: unknown;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "BannerBlock".
 */
export interface BannerBlock {
  style: 'info' | 'warning' | 'error' | 'success';
  content: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  id?: string | null;
  blockName?: string | null;
  blockType: 'banner';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "CodeBlock".
 */
export interface CodeBlock {
  language?: ('typescript' | 'javascript' | 'css') | null;
  code: string;
  id?: string | null;
  blockName?: string | null;
  blockType: 'code';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "auth".
 */
export interface Auth {
  [k: string]: unknown;
}


declare module 'payload' {
  export interface GeneratedTypes extends Config {}
}