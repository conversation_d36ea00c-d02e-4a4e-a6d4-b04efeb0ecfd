import { payloadCloudPlugin } from '@payloadcms/payload-cloud'
import { formBuilderPlugin } from '@payloadcms/plugin-form-builder'
import { nestedDocsPlugin } from '@payloadcms/plugin-nested-docs'
import { redirectsPlugin } from '@payloadcms/plugin-redirects'
import { seoPlugin } from '@payloadcms/plugin-seo'
import { searchPlugin } from '@payloadcms/plugin-search'
import { s3Storage } from '@payloadcms/storage-s3'
import { Plugin } from 'payload'
import { revalidateRedirects } from '@/hooks/revalidateRedirects'
import { GenerateTitle, GenerateURL } from '@payloadcms/plugin-seo/types'
import { FixedToolbarFeature, HeadingFeature, lexicalEditor } from '@payloadcms/richtext-lexical'
import { searchFields } from '@/search/fieldOverrides'
import { beforeSyncWithSearch } from '@/search/beforeSync'

import { Page, Post } from '@/payload-types'
import { getServerSideURL } from '@/utilities/getURL'

const generateTitle: GenerateTitle<Post | Page> = ({ doc }) => {
  return doc?.title ? `${doc.title} | Payload Website Template` : 'Payload Website Template'
}

const generateURL: GenerateURL<Post | Page> = ({ doc }) => {
  const url = getServerSideURL()

  return doc?.slug ? `${url}/${doc.slug}` : url
}

// S3 storage configuration
const useS3 = process.env.S3_BUCKET && process.env.S3_REGION

export const plugins: Plugin[] = [
  // Conditionally add S3 storage plugin
  ...(useS3
    ? [
        s3Storage({
          collections: {
            media: true,
          },
          bucket: process.env.S3_BUCKET!,
          config: {
            region: process.env.S3_REGION!,
            // Use explicit credentials if provided, otherwise let AWS SDK handle automatically
            ...(process.env.S3_ACCESS_KEY_ID && process.env.S3_SECRET_ACCESS_KEY
              ? {
                  credentials: {
                    accessKeyId: process.env.S3_ACCESS_KEY_ID,
                    secretAccessKey: process.env.S3_SECRET_ACCESS_KEY,
                  },
                }
              : {}),
          },
        }),
      ]
    : []),
  redirectsPlugin({
    collections: ['pages', 'posts'],
    overrides: {
      access: {
        admin: ({ req: { user } }) => {
          // Only show in admin panel for admin users
          return Boolean(user && user.role === 'admin')
        },
        create: ({ req: { user } }) => Boolean(user && user.role === 'admin'),
        read: ({ req: { user } }) => Boolean(user && user.role === 'admin'),
        update: ({ req: { user } }) => Boolean(user && user.role === 'admin'),
        delete: ({ req: { user } }) => Boolean(user && user.role === 'admin'),
      },
      // @ts-expect-error - This is a valid override, mapped fields don't resolve to the same type
      fields: ({ defaultFields }) => {
        return defaultFields.map((field) => {
          if ('name' in field && field.name === 'from') {
            return {
              ...field,
              admin: {
                description: 'You will need to rebuild the website when changing this field.',
              },
            }
          }
          return field
        })
      },
      hooks: {
        afterChange: [revalidateRedirects],
      },
    },
  }),
  nestedDocsPlugin({
    collections: ['categories'],
    generateURL: (docs) => docs.reduce((url, doc) => `${url}/${doc.slug}`, ''),
  }),
  seoPlugin({
    generateTitle,
    generateURL,
  }),
  formBuilderPlugin({
    fields: {
      payment: false,
    },
    formOverrides: {
      access: {
        admin: ({ req: { user } }) => {
          // Only show in admin panel for admin users
          return Boolean(user && user.role === 'admin')
        },
        create: ({ req: { user } }) => Boolean(user && user.role === 'admin'),
        read: ({ req: { user } }) => Boolean(user && user.role === 'admin'),
        update: ({ req: { user } }) => Boolean(user && user.role === 'admin'),
        delete: ({ req: { user } }) => Boolean(user && user.role === 'admin'),
      },
      fields: ({ defaultFields }) => {
        return defaultFields.map((field) => {
          if ('name' in field && field.name === 'confirmationMessage') {
            return {
              ...field,
              editor: lexicalEditor({
                features: ({ rootFeatures }) => {
                  return [
                    ...rootFeatures,
                    FixedToolbarFeature(),
                    HeadingFeature({ enabledHeadingSizes: ['h1', 'h2', 'h3', 'h4'] }),
                  ]
                },
              }),
            }
          }
          return field
        })
      },
    },
    formSubmissionOverrides: {
      access: {
        admin: ({ req: { user } }) => {
          // Only show in admin panel for admin users
          return Boolean(user && user.role === 'admin')
        },
        create: ({ req: { user } }) => Boolean(user && user.role === 'admin'),
        read: ({ req: { user } }) => Boolean(user && user.role === 'admin'),
        update: ({ req: { user } }) => Boolean(user && user.role === 'admin'),
        delete: ({ req: { user } }) => Boolean(user && user.role === 'admin'),
      },
    },
  }),
  searchPlugin({
    collections: ['posts'],
    beforeSync: beforeSyncWithSearch,
    searchOverrides: {
      access: {
        admin: ({ req: { user } }) => {
          // Only show in admin panel for admin users
          return Boolean(user && user.role === 'admin')
        },
        create: ({ req: { user } }) => Boolean(user && user.role === 'admin'),
        read: ({ req: { user } }) => Boolean(user && user.role === 'admin'),
        update: ({ req: { user } }) => Boolean(user && user.role === 'admin'),
        delete: ({ req: { user } }) => Boolean(user && user.role === 'admin'),
      },
      fields: ({ defaultFields }) => {
        return [...defaultFields, ...searchFields]
      },
    },
  }),
  payloadCloudPlugin(),
]
