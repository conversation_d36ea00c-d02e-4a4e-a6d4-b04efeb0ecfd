import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'

export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    
    // Fetch only the logo information from site settings
    const siteSettings = await payload.findGlobal({
      slug: 'site-settings',
      depth: 2, // Include related media files
    })

    // Extract only logo-related information for public use
    const logoData = {
      logo: siteSettings?.branding?.logo ? {
        url: siteSettings.branding.logo.url,
        alt: siteSettings.branding.logo.alt || siteSettings.branding?.logoAlt,
        width: siteSettings.branding.logo.width || siteSettings.branding?.logoWidth,
        height: siteSettings.branding.logo.height || siteSettings.branding?.logoHeight,
      } : null,
      siteName: siteSettings?.siteName,
      logoAlt: siteSettings?.branding?.logoAlt,
      logoWidth: siteSettings?.branding?.logoWidth,
      logoHeight: siteSettings?.branding?.logoHeight,
    }

    console.log('Public Logo API: Logo data fetched successfully')
    if (logoData.logo?.url) {
      console.log('Public Logo API: Custom logo available:', logoData.logo.url)
    }

    // Return the logo data with aggressive caching
    return NextResponse.json(logoData, {
      status: 200,
      headers: {
        'Cache-Control': 'public, s-maxage=3600, stale-while-revalidate=7200', // Cache for 1 hour
        'Access-Control-Allow-Origin': '*', // Allow CORS for public access
      },
    })
  } catch (error) {
    console.error('Error fetching logo data:', error)
    
    // Return default values on error
    const defaultLogoData = {
      logo: null,
      siteName: 'Maddox Tees',
      logoAlt: 'Maddox Tees Logo',
      logoWidth: 193,
      logoHeight: 34,
    }

    return NextResponse.json(defaultLogoData, {
      status: 200, // Return 200 with defaults instead of error
      headers: {
        'Cache-Control': 'public, s-maxage=300, stale-while-revalidate=600', // Shorter cache for fallback
        'Access-Control-Allow-Origin': '*',
      },
    })
  }
}
