import { getPayload } from 'payload'
import config from '@payload-config'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    
    // Get the current user from the request
    const authResult = await payload.auth({ headers: request.headers })
    
    if (!authResult.user) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      )
    }

    const user = authResult.user

    // Test access to different collections
    const testResults = []

    // Test Media collection access
    try {
      const mediaResult = await payload.find({
        collection: 'media',
        user,
        limit: 1,
      })
      testResults.push({
        collection: 'media',
        canAccess: true,
        count: mediaResult.totalDocs,
      })
    } catch (error) {
      testResults.push({
        collection: 'media',
        canAccess: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      })
    }

    // Test Products collection access
    try {
      const productsResult = await payload.find({
        collection: 'products',
        user,
        limit: 1,
      })
      testResults.push({
        collection: 'products',
        canAccess: true,
        count: productsResult.totalDocs,
      })
    } catch (error) {
      testResults.push({
        collection: 'products',
        canAccess: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      })
    }

    // Test Orders collection access
    try {
      const ordersResult = await payload.find({
        collection: 'orders',
        user,
        limit: 1,
      })
      testResults.push({
        collection: 'orders',
        canAccess: true,
        count: ordersResult.totalDocs,
      })
    } catch (error) {
      testResults.push({
        collection: 'orders',
        canAccess: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      })
    }

    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
      },
      testResults,
    })

  } catch (error) {
    console.error('Test access error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 })
  }
}
