import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'

export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config })

    // Fetch the site settings global
    const siteSettings = await payload.findGlobal({
      slug: 'site-settings',
      depth: 2, // Include related media files
    })

    // Log for debugging
    console.log('API: Site settings fetched successfully')
    if (siteSettings?.branding?.logo) {
      console.log('API: Custom logo found:', siteSettings.branding.logo.url)
    }

    // Return the site settings data
    return NextResponse.json(siteSettings, {
      status: 200,
      headers: {
        'Cache-Control': 'public, s-maxage=300, stale-while-revalidate=600', // Cache for 5 minutes
      },
    })
  } catch (error) {
    console.error('Error fetching site settings:', error)

    // Return default values on error
    const defaultSettings = {
      siteName: 'Maddox Tees',
      siteDescription: 'Premium B2B T-shirt printing and DTF services',
      branding: {
        logoAlt: '<PERSON> Tees Logo',
        logoWidth: 193,
        logoHeight: 34,
      },
    }

    return NextResponse.json(defaultSettings, {
      status: 200, // Return 200 with defaults instead of error
      headers: {
        'Cache-Control': 'public, s-maxage=60, stale-while-revalidate=120', // Shorter cache for fallback
      },
    })
  }
}
