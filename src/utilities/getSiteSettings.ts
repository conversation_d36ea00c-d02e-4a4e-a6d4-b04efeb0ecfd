import { getPayload } from 'payload'
import config from '@payload-config'
import type { SiteSettings } from '@/payload-types'

interface SiteSettingsData {
  siteName?: string
  siteDescription?: string
  branding?: {
    logo?: {
      id: string
      url: string
      alt?: string
      width?: number
      height?: number
    }
    logoAlt?: string
    logoWidth?: number
    logoHeight?: number
    favicon?: {
      id: string
      url: string
      alt?: string
    }
  }
  contact?: {
    email?: string
    phone?: string
    address?: string
  }
  social?: {
    facebook?: string
    instagram?: string
    twitter?: string
    linkedin?: string
  }
  seo?: {
    metaTitle?: string
    metaDescription?: string
    ogImage?: {
      id: string
      url: string
      alt?: string
    }
  }
}

/**
 * Server-side function to get site settings from Payload CMS
 * This function can only be used in server components and API routes
 */
export const getSiteSettingsServer = async (): Promise<SiteSettingsData | null> => {
  try {
    const payload = await getPayload({ config })
    
    const siteSettings = await payload.findGlobal({
      slug: 'site-settings',
      depth: 2, // Include related media files
    })

    return siteSettings as SiteSettingsData
  } catch (error) {
    console.error('Error fetching site settings on server:', error)
    
    // Return default values on error
    return {
      siteName: 'Maddox Tees',
      siteDescription: 'Premium B2B T-shirt printing and DTF services',
      branding: {
        logoAlt: 'Maddox Tees Logo',
        logoWidth: 193,
        logoHeight: 34,
      },
    }
  }
}

/**
 * Get logo information specifically for server-side rendering
 */
export const getLogoServer = async () => {
  const siteSettings = await getSiteSettingsServer()
  
  const logo = siteSettings?.branding?.logo
  const logoSrc = logo?.url || "https://raw.githubusercontent.com/payloadcms/payload/main/packages/ui/src/assets/payload-logo-light.svg"
  const altText = logo?.alt || siteSettings?.branding?.logoAlt || "Maddox Tees Logo"
  const width = logo?.width || siteSettings?.branding?.logoWidth || 193
  const height = logo?.height || siteSettings?.branding?.logoHeight || 34

  return {
    src: logoSrc,
    alt: altText,
    width,
    height,
    hasCustomLogo: !!logo?.url,
  }
}
