'use client'

import { useEffect, useState } from 'react'
import type { SiteSettings } from '@/payload-types'

interface SiteSettingsData {
  siteName?: string
  siteDescription?: string
  branding?: {
    logo?: {
      id: string
      url: string
      alt?: string
      width?: number
      height?: number
    }
    logoAlt?: string
    logoWidth?: number
    logoHeight?: number
    favicon?: {
      id: string
      url: string
      alt?: string
    }
  }
  contact?: {
    email?: string
    phone?: string
    address?: string
  }
  social?: {
    facebook?: string
    instagram?: string
    twitter?: string
    linkedin?: string
  }
  seo?: {
    metaTitle?: string
    metaDescription?: string
    ogImage?: {
      id: string
      url: string
      alt?: string
    }
  }
}

export const useSiteSettings = () => {
  const [siteSettings, setSiteSettings] = useState<SiteSettingsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchSiteSettings = async () => {
      try {
        setLoading(true)
        setError(null)

        const response = await fetch('/api/site-settings', {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        })

        if (!response.ok) {
          throw new Error(`Failed to fetch site settings: ${response.status}`)
        }

        const data = await response.json()
        setSiteSettings(data)
      } catch (err) {
        console.error('Error fetching site settings:', err)
        setError(err instanceof Error ? err.message : 'Failed to fetch site settings')
        // Set default values on error
        setSiteSettings({
          siteName: 'Maddox Tees',
          siteDescription: 'Premium B2B T-shirt printing and DTF services',
          branding: {
            logoAlt: 'Maddox Tees Logo',
            logoWidth: 193,
            logoHeight: 34,
          },
        })
      } finally {
        setLoading(false)
      }
    }

    fetchSiteSettings()
  }, [])

  return {
    siteSettings,
    loading,
    error,
    // Helper functions for easy access to common values
    siteName: siteSettings?.siteName || 'Maddox Tees',
    logo: siteSettings?.branding?.logo,
    logoAlt: siteSettings?.branding?.logoAlt || 'Maddox Tees Logo',
    logoWidth: siteSettings?.branding?.logoWidth || 193,
    logoHeight: siteSettings?.branding?.logoHeight || 34,
    favicon: siteSettings?.branding?.favicon,
    contact: siteSettings?.contact,
    social: siteSettings?.social,
    seo: siteSettings?.seo,
  }
}

// Server-side function to get site settings
export const getSiteSettings = async (): Promise<SiteSettingsData | null> => {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_SERVER_URL || 'http://localhost:3000'}/api/site-settings`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        // Don't include credentials for server-side requests
      },
    )

    if (!response.ok) {
      console.error(`Failed to fetch site settings: ${response.status}`)
      return null
    }

    const data = await response.json()
    return data
  } catch (err) {
    console.error('Error fetching site settings:', err)
    return null
  }
}
