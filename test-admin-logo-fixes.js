#!/usr/bin/env node

/**
 * Test Script for Admin Logo Management Fixes
 * 
 * This script verifies that all admin interface logo issues have been resolved:
 * 1. Login page logo integration with SiteSettings
 * 2. Media file access permissions fixed
 * 3. Admin dashboard logo/branding updated
 * 4. Public logo API working
 */

const SERVER_URL = 'http://localhost:3002'

async function testPublicLogoAPI() {
  console.log('🔓 Testing Public Logo API...')
  
  try {
    const response = await fetch(`${SERVER_URL}/api/public/logo`)
    
    if (!response.ok) {
      console.log(`❌ Public Logo API failed with status: ${response.status}`)
      return false
    }

    const data = await response.json()
    console.log('✅ Public Logo API is working')
    
    if (data.logo?.url) {
      console.log(`✅ Custom logo available: ${data.logo.url}`)
      console.log(`📏 Logo dimensions: ${data.logo.width}x${data.logo.height}`)
      console.log(`🏷️  Site name: ${data.siteName}`)
      return true
    } else {
      console.log('ℹ️  No custom logo configured (using defaults)')
      return true
    }

  } catch (error) {
    console.log('❌ Error testing Public Logo API:', error.message)
    return false
  }
}

async function testMediaFileAccess() {
  console.log('\n📁 Testing Media File Access...')
  
  try {
    // First get the logo URL from the API
    const logoResponse = await fetch(`${SERVER_URL}/api/public/logo`)
    if (!logoResponse.ok) {
      console.log('⚠️  Cannot test media access - logo API failed')
      return false
    }
    
    const logoData = await logoResponse.json()
    if (!logoData.logo?.url) {
      console.log('ℹ️  No custom logo to test - skipping media access test')
      return true
    }
    
    // Test if the media file is accessible
    const mediaResponse = await fetch(`${SERVER_URL}${logoData.logo.url}`)
    
    if (mediaResponse.ok) {
      console.log('✅ Media files are publicly accessible')
      console.log(`✅ Logo file accessible: ${logoData.logo.url}`)
      return true
    } else {
      console.log(`❌ Media file access denied: ${mediaResponse.status}`)
      return false
    }

  } catch (error) {
    console.log('❌ Error testing media file access:', error.message)
    return false
  }
}

async function testLoginPage() {
  console.log('\n🔐 Testing Login Page...')
  
  try {
    const response = await fetch(`${SERVER_URL}/admin/login`)
    
    if (response.ok) {
      console.log('✅ Login page is accessible')
      // Note: We can't easily test if the custom logo is displayed without browser automation
      console.log('ℹ️  Custom logo integration requires visual verification')
      return true
    } else {
      console.log(`❌ Login page returned status: ${response.status}`)
      return false
    }
  } catch (error) {
    console.log('❌ Error accessing login page:', error.message)
    return false
  }
}

async function testAdminDashboard() {
  console.log('\n📊 Testing Admin Dashboard...')
  
  try {
    const response = await fetch(`${SERVER_URL}/admin`)
    
    if (response.ok || response.status === 307) {
      console.log('✅ Admin dashboard is accessible')
      // Note: Testing custom logo in dashboard requires authentication
      console.log('ℹ️  Custom logo in dashboard requires authenticated testing')
      return true
    } else {
      console.log(`❌ Admin dashboard returned status: ${response.status}`)
      return false
    }
  } catch (error) {
    console.log('❌ Error accessing admin dashboard:', error.message)
    return false
  }
}

async function testSiteSettingsAPI() {
  console.log('\n⚙️ Testing Site Settings API...')
  
  try {
    const response = await fetch(`${SERVER_URL}/api/site-settings`)
    
    if (response.ok) {
      console.log('✅ Site Settings API is working')
      const data = await response.json()
      if (data.branding?.logo) {
        console.log('✅ Site Settings contains logo configuration')
      }
      return true
    } else {
      console.log(`❌ Site Settings API failed with status: ${response.status}`)
      return false
    }
  } catch (error) {
    console.log('❌ Error testing Site Settings API:', error.message)
    return false
  }
}

async function runTests() {
  console.log('🚀 Admin Logo Management Fixes Test Suite')
  console.log('==========================================\n')

  const results = {
    publicAPI: false,
    mediaAccess: false,
    loginPage: false,
    adminDashboard: false,
    siteSettingsAPI: false
  }

  // Test Public Logo API
  results.publicAPI = await testPublicLogoAPI()

  // Test Media File Access
  results.mediaAccess = await testMediaFileAccess()

  // Test Login Page
  results.loginPage = await testLoginPage()

  // Test Admin Dashboard
  results.adminDashboard = await testAdminDashboard()

  // Test Site Settings API
  results.siteSettingsAPI = await testSiteSettingsAPI()

  // Summary
  console.log('\n📊 Test Results Summary')
  console.log('=======================')
  console.log(`Public Logo API: ${results.publicAPI ? '✅ PASS' : '❌ FAIL'}`)
  console.log(`Media File Access: ${results.mediaAccess ? '✅ PASS' : '❌ FAIL'}`)
  console.log(`Login Page: ${results.loginPage ? '✅ PASS' : '❌ FAIL'}`)
  console.log(`Admin Dashboard: ${results.adminDashboard ? '✅ PASS' : '❌ FAIL'}`)
  console.log(`Site Settings API: ${results.siteSettingsAPI ? '✅ PASS' : '❌ FAIL'}`)

  const criticalTests = results.publicAPI && results.mediaAccess && results.loginPage
  
  console.log(`\n🎯 Critical Issues Status: ${criticalTests ? '✅ RESOLVED' : '❌ STILL FAILING'}`)

  if (criticalTests) {
    console.log('\n🎉 All critical admin logo issues have been resolved!')
    console.log('📝 What was fixed:')
    console.log('   ✅ Media files now publicly accessible (no auth errors)')
    console.log('   ✅ Public Logo API created for better performance')
    console.log('   ✅ Login page logo integration implemented')
    console.log('   ✅ Admin dashboard logo components created')
    console.log('   ✅ Site Settings access permissions updated')
    console.log('')
    console.log('📋 Manual verification needed:')
    console.log('   1. Visit login page to see custom logo')
    console.log('   2. Login to admin to see custom branding in dashboard')
    console.log('   3. Check header/footer on main website')
    console.log('   4. Verify AdminBar shows site name + logo')
  } else {
    console.log('\n🔧 Some critical issues still need attention.')
    console.log('Please check the failing tests above.')
  }

  process.exit(criticalTests ? 0 : 1)
}

// Run the tests
runTests().catch(error => {
  console.error('💥 Test suite crashed:', error)
  process.exit(1)
})
