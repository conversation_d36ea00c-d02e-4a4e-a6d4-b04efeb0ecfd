#!/usr/bin/env node

/**
 * Test Script for Site Settings Implementation
 *
 * This script tests the Site Settings functionality to ensure:
 * 1. Site Settings Global is accessible
 * 2. API endpoint works correctly
 * 3. Default values are returned when no settings exist
 * 4. S3 integration is properly configured
 */

// Use built-in fetch (Node.js 18+) or import node-fetch
const fetch = globalThis.fetch || (await import('node-fetch')).default

const SERVER_URL = process.env.NEXT_PUBLIC_SERVER_URL || 'http://localhost:3002'

async function testSiteSettingsAPI() {
  console.log('🧪 Testing Site Settings Implementation...\n')

  try {
    console.log('1. Testing Site Settings API endpoint...')
    const response = await fetch(`${SERVER_URL}/api/site-settings`)

    if (!response.ok) {
      console.log(`❌ API request failed with status: ${response.status}`)
      return false
    }

    const data = await response.json()
    console.log('✅ API endpoint is working')
    console.log('📄 Response data:', JSON.stringify(data, null, 2))

    // Test default values
    console.log('\n2. Checking default values...')
    if (data.siteName) {
      console.log(`✅ Site name: ${data.siteName}`)
    } else {
      console.log('⚠️  No site name found, using fallback')
    }

    if (data.branding) {
      console.log('✅ Branding configuration exists')
      if (data.branding.logo) {
        console.log(`✅ Custom logo found: ${data.branding.logo.url}`)
      } else {
        console.log('ℹ️  No custom logo, will use default')
      }
    } else {
      console.log('ℹ️  No branding configuration, will use defaults')
    }

    console.log('\n3. Testing cache headers...')
    const cacheControl = response.headers.get('cache-control')
    if (cacheControl) {
      console.log(`✅ Cache headers present: ${cacheControl}`)
    } else {
      console.log('⚠️  No cache headers found')
    }

    return true
  } catch (error) {
    console.log('❌ Error testing Site Settings API:', error.message)
    return false
  }
}

async function testAdminAccess() {
  console.log('\n4. Testing admin panel access...')

  try {
    const response = await fetch(`${SERVER_URL}/admin`)

    if (response.ok) {
      console.log('✅ Admin panel is accessible')
      return true
    } else {
      console.log(`⚠️  Admin panel returned status: ${response.status}`)
      return false
    }
  } catch (error) {
    console.log('❌ Error accessing admin panel:', error.message)
    return false
  }
}

async function testS3Configuration() {
  console.log('\n5. Checking S3 configuration...')

  const requiredEnvVars = ['S3_BUCKET', 'S3_REGION']
  let allPresent = true

  requiredEnvVars.forEach((envVar) => {
    if (process.env[envVar]) {
      console.log(`✅ ${envVar}: ${process.env[envVar]}`)
    } else {
      console.log(`❌ Missing environment variable: ${envVar}`)
      allPresent = false
    }
  })

  if (process.env.AWS_PROFILE) {
    console.log(`✅ AWS_PROFILE: ${process.env.AWS_PROFILE}`)
  } else if (process.env.S3_ACCESS_KEY_ID && process.env.S3_SECRET_ACCESS_KEY) {
    console.log('✅ AWS credentials configured via environment variables')
  } else {
    console.log('⚠️  No AWS credentials found (profile or env vars)')
    allPresent = false
  }

  return allPresent
}

async function runTests() {
  console.log('🚀 Maddox Tees Site Settings Test Suite')
  console.log('=====================================\n')

  const results = {
    api: false,
    admin: false,
    s3: false,
  }

  // Test API
  results.api = await testSiteSettingsAPI()

  // Test Admin Access
  results.admin = await testAdminAccess()

  // Test S3 Configuration
  results.s3 = testS3Configuration()

  // Summary
  console.log('\n📊 Test Results Summary')
  console.log('=======================')
  console.log(`API Endpoint: ${results.api ? '✅ PASS' : '❌ FAIL'}`)
  console.log(`Admin Access: ${results.admin ? '✅ PASS' : '❌ FAIL'}`)
  console.log(`S3 Config: ${results.s3 ? '✅ PASS' : '❌ FAIL'}`)

  const allPassed = results.api && results.admin && results.s3

  console.log(`\n🎯 Overall Status: ${allPassed ? '✅ ALL TESTS PASSED' : '⚠️  SOME TESTS FAILED'}`)

  if (allPassed) {
    console.log('\n🎉 Site Settings implementation is working correctly!')
    console.log('📝 Next steps:')
    console.log('   1. Login to admin panel at /admin')
    console.log('   2. Navigate to Site Configuration > Site Settings')
    console.log('   3. Upload your custom logo')
    console.log('   4. Configure branding and contact information')
    console.log('   5. Test the frontend to see your changes')
  } else {
    console.log('\n🔧 Please fix the failing tests before proceeding.')
  }

  process.exit(allPassed ? 0 : 1)
}

// Run the tests
runTests().catch((error) => {
  console.error('💥 Test suite crashed:', error)
  process.exit(1)
})
